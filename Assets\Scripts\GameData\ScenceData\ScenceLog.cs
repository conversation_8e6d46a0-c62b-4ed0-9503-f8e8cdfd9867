using System.Collections;
using System.Collections.Generic;
using Unity.Mathematics;
using UnityEditor;
using UnityEngine;

public class ScenceLog : MonoBehaviour
{
    public static ScenceLog Instance { get; private set; } // 单例模式
    //记录当前bufftypeid对应的buffid
    private Dictionary<int, int> buffTypeToId = new Dictionary<int, int>();
    //DPS统计 
    private Dictionary<CharacterType, List<int>> _dpsDict = new Dictionary<CharacterType, List<int>>();
    private Dictionary<CharacterType, float> _dps = new Dictionary<CharacterType, float>();
    public Dictionary<CharacterType, float> Dps
    {
        get { return _dps; }
    }
    private Dictionary<CharacterType, float> _heal = new Dictionary<CharacterType, float>();
    public Dictionary<CharacterType, float> Heal
    {
        get { return _heal; }
    }
    private float _countSeconds = 5f; //统计时间间隔
    private float _playTime = 0f; //游戏时间    
    public float PlayTime
    {
        get { return _playTime; }
    }


    //解锁的元素反应
    private Dictionary<string, bool> _unlockedElementReactions = new Dictionary<string, bool>();

    public void SetElementReaction(string elementReaction, bool isUnlocked)
    {
        if (_unlockedElementReactions.ContainsKey(elementReaction))
        {
            _unlockedElementReactions[elementReaction] = isUnlocked;
        }
        else
        {
            _unlockedElementReactions.Add(elementReaction, isUnlocked);
        }
    }

    public bool isUnlockedElementReaction(string elementReaction)
    {
        if (_unlockedElementReactions.ContainsKey(elementReaction))
        {
            return _unlockedElementReactions[elementReaction];
        }
        return false;
    }




    public void AddBuffTypeToId(int buffTypeId, int buffId)
    {
        if (!buffTypeToId.ContainsKey(buffTypeId))
        {
            buffTypeToId.Add(buffTypeId, buffId);
        }
        else
        {
            buffTypeToId[buffTypeId] = buffId;
        }
    }
    public int GetBuffIdByType(int buffTypeId)
    {
        if (buffTypeToId.ContainsKey(buffTypeId))
        {
            return buffTypeToId[buffTypeId];
        }
        return buffTypeId;
    }

    //用一个dict记录场景中的实践发生量，包括敌人死亡数，生成数，buff获取数等信息
    public Dictionary<string, int> ScenceStats = new Dictionary<string, int>();

    private Dictionary<int, int> _skillReleaseTimes = new Dictionary<int, int>();
    public Dictionary<int, int> SkillReleaseTimes
    {
        get { return _skillReleaseTimes; }
    }

    //初始化
    public void InitStats()
    {
        //清空dict
        ScenceStats.Clear();
    }
    public void AddStats(string key, int value)
    {
        //添加key-value对，如果key已存在则value相加
        if (ScenceStats.ContainsKey(key))
        {
            ScenceStats[key] += value;
        }
        else
        {
            ScenceStats.Add(key, value);
        }
    }

    public void SetStats(string key, int value)
    {
        //添加key-value对，如果key已存在则value相加
        if (ScenceStats.ContainsKey(key))
        {
            ScenceStats[key] = value;
        }
        else
        {
            ScenceStats.Add(key, value);
        }
    }
    private void Update()
    {
        //更新场紧中被控制的敌人数量
        //获取tag为Enemy和boss的对象
        GameObject[] enemies = GameObject.FindGameObjectsWithTag("Enemy");
        GameObject[] bosses = GameObject.FindGameObjectsWithTag("Boss");
        int underControl = 0;
        //遍历所有敌人，如果敌人的状态为被控制，则underControl加1
        foreach (GameObject enemy in enemies)
        {
            if (enemy.GetComponent<BuffManager>().IsUnderControl)
            {
                underControl++;
            }
        }
        foreach (GameObject boss in bosses)
        {
            if (boss.GetComponent<BuffManager>().IsUnderControl)
            {
                underControl++;
            }
        }
        //更新场景中被控制的敌人数量
        SetStats("EnemyUnderControl", underControl);

        //更新场景中召唤物的存活数量
        GameObject[] summors = GameObject.FindGameObjectsWithTag("Summor");
        SetStats("SummorAlive", summors.Length);

        //更新场景中防御塔的存活数量
        GameObject[] towers = GameObject.FindGameObjectsWithTag("Tower");
        SetStats("TowerAlive", towers.Length);

    }

    private void FixedUpdate()
    {
        //更新游戏时间
        _playTime += Time.fixedDeltaTime;
    }
    public void Subscribe(AttributeProperty attributeProperty)
    {
        attributeProperty.HpChanged += OnHealthChanged;
    }

    public void Unsubscribe(AttributeProperty attributeProperty)
    {
        attributeProperty.HpChanged -= OnHealthChanged;
    }

    private void OnHealthChanged(CharacterType characterType, int amount)
    {
        //记录每个characterType受到的伤害和治疗
        if (!_dpsDict.ContainsKey(characterType))
        {
            _dpsDict.Add(characterType, new List<int>());
        }
        //在对应的list中添加伤害值
        _dpsDict[characterType].Add(amount);
    }

    private IEnumerator CalculateDPS()
    {
        while (true)
        {
            yield return new WaitForSeconds(_countSeconds);

            int totalDPS = 0;
            int totalHeal = 0;
            //先将所有的DPS和Heal清零
            List<CharacterType> keys = new List<CharacterType>(_dps.Keys);
            foreach (var key in keys)
            {
                _dps[key] = 0;
            }
            keys = new List<CharacterType>(_heal.Keys);
            foreach (var key in keys)
            {
                _heal[key] = 0;
            }
            // 计算并输出所有敌人的总伤害和治疗，更新到DPS记录中
            foreach (var pair in _dpsDict)
            {
                foreach (var amount in pair.Value)
                {
                    if (amount < 0)
                    {
                        totalDPS += math.abs(amount);
                    }
                    else
                    {
                        totalHeal += amount;
                    }
                }
                _dps[pair.Key] = totalDPS / _countSeconds;
                _heal[pair.Key] = totalHeal / _countSeconds;
            }
            //_dps和_heal中没有被更新的characterType，说明这个characterType在这段时间内没有受到伤害或治疗
            //清空记录
            _dpsDict.Clear();
        }
    }

    private void SetChangeAbleProperty(string propertyField, float coefficient)
    {
        SetStats(propertyField, (int)coefficient);
    }

    public void AddSkillReleaseTimes(int skillId, int value = 1)
    {
        if (!_skillReleaseTimes.ContainsKey(skillId))
        {
            _skillReleaseTimes.Add(skillId, value);
        }
        else
        {
            _skillReleaseTimes[skillId]++;
        }
    }


    // Start is called before the first frame update
    void Awake()
    {
        //单例模式
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject); // 如果需要，保持实例在场景加载时不销毁
        }
        else
        {
            Destroy(gameObject);
        }
        //初始化
        InitStats();
        string[] _keys = { "EnemyDeath",  //敌人死亡数
                           "EnemySpawn",  //敌人生成数
                           "BossDeath",  //首领死亡数
                           "BuffGet",   //buff获取数
                           "EnemyUnderControl", //被控制的敌人数
                           "SummorAlive", //召唤物存活数
                           "TowerAlive", //防御塔存活数
                           "MaxChainTargets", //闪电最大连锁目标数  
        };
        foreach (string key in _keys)
        {
            AddStats(key, 0);
        }
        //注册bufftypeid和buffid的对应关系
        AddBuffTypeToId(1000, 1000);
        AddBuffTypeToId(1002, 1002);
        AddBuffTypeToId(1003, 1003);
        AddBuffTypeToId(1004, 1004);
        AddBuffTypeToId(1005, 1005);
        AddBuffTypeToId(1008, 1008);
        AddBuffTypeToId(1010, 1010);

        //注册解锁的元素反应

        _unlockedElementReactions = new Dictionary<string, bool>()
        {
            {"Melt", false},
            {"Freeze", false},
            {"Burn", false},
            {"Shock", false},
            {"Ignite", false},
            {"Swirl", false},
            {"Explode", false},
            {"Conduct", false},
            {"Vitrify", false},
            {"Rot", false}
        };
    }

    private void Start()
    {
        //开始计算DPS
        StartCoroutine(CalculateDPS());
    }

    public Dictionary<string, string> GameEndBaseInfo()
    {
        //游戏结束时，返回需要显示的信息
        //包括存活时间，击杀数，玩家等级
        Dictionary<string, string> baseInfo = new Dictionary<string, string>();
        baseInfo.Add("PlayTime", _playTime.ToString("F2"));
        baseInfo.Add("EnemyDeath", ScenceStats["EnemyDeath"].ToString());
        baseInfo.Add("BossDeath", ScenceStats["BossDeath"].ToString());
        int _playerLevel = PlayerController.Instance.level;
        baseInfo.Add("PlayerLevel", _playerLevel.ToString());
        return baseInfo;
    }

    //获取属性值
    public float GetPropertyValue(string propertyName)
    {
        if (!ScenceStats.ContainsKey(propertyName))
        {
            return 0f; // 或者抛出异常
        }
        return ScenceStats[propertyName];
    }
    
}
