using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Reflection;
using System.Xml;
using System;
using System.Data;
using System.Linq;
using LevelUpSytem;


public class LevelUpManager : MonoBehaviour
{
    public static LevelUpManager Instance { get; private set; } // 单例模式
    // Start is called before the first frame update
    private BuffData[] buffBasePool;
    public Dictionary<string, bool> statusIsActivate;  //特殊状态是否激活
    public Dictionary<string, float> statusEffectLevel;  //特殊状态数值
    public GameObject UI;
    private UIController uiController;
    //每次升级时需要广播的状态变量
    public Dictionary<string, float> elementCoefficient; //元素伤害加成

    private Dictionary<(string, string), float> elementReactionCoefficientInit;//元素反应初始数值
    public Dictionary<(string, string), float> elementReactionCoefficient; //元素反应伤害值
    public static Dictionary<(string, string), (string, int)> elementReactionDict; //元素反应字典
    public Dictionary<int, int> buffMaxStackLayer; //buff最大叠加层数 : {buff.id:maxStackNum}

    public Dictionary<int, Dictionary<string, float>> buffCoefficient;// {buffTypeID: {fieldName:coefficient}}
    //只同步最新更新的存储变量（新建对象需要同步字典）
    public Dictionary<int, BuffData> levelupBuffPool; //已选buff池
    public Dictionary<int, string> buffTypeDict; //记录了XML中所有的buffTppeId和buffTypeName的对应关系
    private CharacterLevelUpSytem characterLevelUpSytem;
    private ScenceLog scenceLog;
    void Awake()
    {
        // 单例模式
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject); // 如果需要在场景加载时保持实例
        }
        else
        {
            if (Instance != this)
            {
                Destroy(gameObject); // 避免多个实例存在
            }
        }
        // Load all BuffData from the resource folder
        buffBasePool = Resources.LoadAll<BuffData>("Data/BuffData");
        
        // Load UI
        if (UI == null)
        {
            UI = GameObject.FindGameObjectWithTag("UI");   
        }
        


        // 初始化元素加成系数
        elementCoefficient = new Dictionary<string, float>()
        {
            {"Fire", 1f},
            {"Ice", 1f},
            {"Wood", 1f},
            {"Wind", 1f},
            {"Dark", 1f},
            {"Light", 1f},
            {"NA", 1f}
        };

        // 初始化元素反应倍率
        elementReactionDict = ReadElementReactionXML();
        elementReactionCoefficientInit = ReadElementReactionCoefXML();
        elementReactionCoefficient = ReadElementReactionCoefXML();

        //初始化特殊状态是否激活
        statusIsActivate = new Dictionary<string, bool>() 
        {
        };

        //初始化特殊状态数值
        statusEffectLevel = new Dictionary<string, float>()
        {
        };

        //初始化
        levelupBuffPool = new Dictionary<int, BuffData>();
        buffMaxStackLayer = new Dictionary<int, int>();
        buffCoefficient = new Dictionary<int, Dictionary<string, float>>();
        buffTypeDict = new Dictionary<int, string>();
        characterLevelUpSytem = GetComponent<CharacterLevelUpSytem>();                           
    }

    private void Start()
    {
        uiController = UI.GetComponent<UIController>();
        
        //Initialize buffMaxStackLayer Dict
        foreach (BuffData buff in buffBasePool)
        {
            if (!buffMaxStackLayer.ContainsKey(buff.id))
            {
                buffMaxStackLayer.Add(buff.id, buff.maxStackLayer);
            }
        }

        ReadBuffTypeXML();
        scenceLog = ScenceLog.Instance;

    }
    private Dictionary<(string, string), (string,int)> ReadElementReactionXML()
    {
        Dictionary<(string, string), (string, int)> elementReactionDict = new Dictionary<(string, string), (string, int)>();

        XmlReader reader = XmlReader.Create(Application.dataPath + "/Config/elementReaction.xml");
        while (reader.Read())
        {
            if (reader.NodeType == XmlNodeType.Element && reader.Name == "reaction")
            {
                string element1 = reader.GetAttribute("element1");
                string element2 = reader.GetAttribute("element2");
                string method = reader.GetAttribute("method");
                int damage = int.Parse(reader.GetAttribute("damage"));
                elementReactionDict.Add((element1, element2), (method, damage));
            }
        }

        return elementReactionDict;
    }


    private Dictionary<(string, string), float> ReadElementReactionCoefXML()
    {
        Dictionary<(string, string), float> elementReactionCoef = new Dictionary<(string, string), float>();

        XmlReader reader = XmlReader.Create(Application.dataPath + "/Config/elementReactionCoef.xml");
        while (reader.Read())
        {
            if (reader.NodeType == XmlNodeType.Element && reader.Name == "reaction")
            {
                string element1 = reader.GetAttribute("element1");
                string element2 = reader.GetAttribute("element2");
                float coeff = float.Parse(reader.GetAttribute("coeff"));
                elementReactionCoef.Add((element1, element2), coeff);
            }
        }

        return elementReactionCoef;
    }

    void ReadBuffTypeXML()
    {
        XmlDocument xmlDoc = new XmlDocument(); // Create an XML document object
        xmlDoc.Load(Application.dataPath + "/Config/buffIDMapping.xml"); // Load the XML document from the specified file

        // Get elements
        XmlNodeList buffList = xmlDoc.GetElementsByTagName("bufftype"); // Get elements with tag "bufftype"

        foreach (XmlNode buffTypeInfo in buffList)
        {
            XmlNodeList buffTypeContent = buffTypeInfo.ChildNodes;

            int buffTypeID = -1;
            string buffTypeName = "";

            foreach (XmlNode buffTypeItems in buffTypeContent) // Loop through the items inside the buff
            {
                if (buffTypeItems.Name == "buffTypeID")
                {
                    int.TryParse(buffTypeItems.InnerText, out buffTypeID); // Get the buffTypeID
                }

                if (buffTypeItems.Name == "buffTypeName")
                {
                    buffTypeName = buffTypeItems.InnerText; // Get the buffTypeName
                }
            }

            //print(buffTypeID+ " "+buffTypeName);
            buffTypeDict.Add(buffTypeID, buffTypeName);

        }
    }


    //private LevelUpData loadLevelUpCard(string cardNmae)
    //{
    //    LevelUpData cardData = Resources.Load<LevelUpData>("Data/LevelUpData/" + cardNmae);
    //    return cardData;
    //}
    private List<LevelUpData> randomPickupCardOptions(int n)
    {
        return characterLevelUpSytem.GetLevelUpOptions(n);
    }

    public void Upgrade(int n)
    {
        //通知UI呼出升级界面 
        List<LevelUpData> picked_cards = randomPickupCardOptions(n);
        uiController.Upgrade(picked_cards); 
    }

    public void ApplyUpgrade(LevelUpData[] cards)
    {
        if (cards.Length == 0)
        {
            return;
        }
        
        foreach (LevelUpData card in cards)
        {
            characterLevelUpSytem.AddLevelUpOption(card.optionId);
            ApplyLevelUpOption(card);
        }
    }



    private void ApplyLevelUpOption(LevelUpData card)
    {
        //依据输入的卡片实现卡片效果

        //获取卡片对应的buff基础数值
        int buffId = card.optionId;
        BuffData buff = Resources.Load<BuffData>("Data/BuffData/" + buffId);
        float _elementReactionCoefficient = card.elementReactionCoefficient;
        List<DictData> _buffCoefficientList = card.buffCoefficient;
        List<IdDictData> _buffMaxStackLayer = card.buffMaxStackLayer;

        //需要被广播更新的buffTypeId
        List<int> buffTypeIdsToUpdate = card.affectedBuffTypeIds;

        //通过添加buff的形式，广播升级效果：基础数值加成，特殊状态激活
        UpdateBuffToPool(buff);
        UpdateBuffToManager(buffId);


        //广播buff系数更新
        if (_buffCoefficientList.Count > 0)
        {
            foreach (int buffTypeId in buffTypeIdsToUpdate)
            {
                foreach (DictData coefficient in _buffCoefficientList)
                {
                    //校验 coefficient.key是否合法的BuffData field
                    if(!isValidFiled(coefficient.key, buff))
                    {
                        Debug.LogError("BuffData does not have field " + coefficient.key + ",levelup card with buffid " + buffId + " is not valid");
                        return;
                    }

                    if (buffCoefficient.ContainsKey(buffTypeId))
                    {
                        //不同升级选项对同一个buffTypeId的影响的修改会存在问题
                        buffCoefficient[buffTypeId][coefficient.key] = coefficient.value;
                    }
                    else
                    {
                        buffCoefficient[buffTypeId] = new Dictionary<string, float>();
                        buffCoefficient[buffTypeId][coefficient.key] = coefficient.value;
                    }
                }
             
                UpdateBuffCoefToManager(buffTypeId, buffCoefficient[buffTypeId]);
            }
        }


        //修改元素反应加成
        if (_elementReactionCoefficient != 0)
        {
            //统一修改所有元素反应加成
            foreach (KeyValuePair<(string, string), float> entry in elementReactionCoefficientInit)
            {
                (string, string) elementPair = entry.Key;
                elementReactionCoefficient[elementPair] = elementReactionCoefficientInit[elementPair] * _elementReactionCoefficient;
                UpdateElementReactionCoefToManager(elementPair, elementReactionCoefficient[elementPair]);
            }
        }



        //修改最大叠加层数加成
        if (_buffMaxStackLayer != null)
        {

            foreach (IdDictData data in _buffMaxStackLayer)
            {
                if (buffMaxStackLayer.ContainsKey(data.key))
                {
                    buffMaxStackLayer[data.key] = data.value;
                }
                else
                {
                    buffMaxStackLayer[data.key] = data.value;
                }
                //把最大层数广播到buffManager
                UpdateBuffMaxStackLayerToManager(data.key, buffMaxStackLayer[data.key]);
            }
        }
    }

    public void UpdateBuffToPool(BuffData buff, string updateType="update")
    {
        //updateType 限定为"update"或"replace"
        if (updateType != "update" && updateType != "replace")
        {
            Debug.LogError("updateType should be 'update' or 'replace'");
            return;
        }
        //更新buff到已选升级buff池
        if (levelupBuffPool.ContainsKey(buff.id))
        {
            if (updateType == "replace")
            {
                levelupBuffPool[buff.id] = buff;
            }
            else
            {
                levelupBuffPool[buff.id].buffLevel++;
            }  
        }
        else
        {
            levelupBuffPool[buff.id] = buff;
        }
    }

    private void UpdateElementReactionCoefToManager((string, string) elementPair, float coefficient)
    {
        foreach (string controller_type in new string[] { "Player", "Enemy", "Tower", "Summor", "Boss" })
        {
            GameObject[] objectsInLayer = GameObject.FindGameObjectsWithTag(controller_type);
            foreach (GameObject obj in objectsInLayer)
            {
                //print("UpdateElementReactionCoefToManager: " + obj.name + " update element reaction coef");
                BuffManager buffManager = obj.GetComponent<BuffManager>();
                if (buffManager != null)
                {
                    buffManager.UpdateElementReactionCoefficient(elementPair, coefficient);
                }
            }
        }
    }
    private void UpdateBuffMaxStackLayerToManager(int buffTypeId, int maxStackLayer)
    {
        foreach (string controller_type in new string[] { "Player", "Enemy", "Tower", "Summor", "Boss"})
        {
            //TODO: 只判断受影响层的对象
            //if (buff.isLayerActive(controller_type))
            //{
            GameObject[] objectsInLayer = GameObject.FindGameObjectsWithTag(controller_type);
            foreach (GameObject obj in objectsInLayer)
            {
                //print("UpdateBuffToManager: " + obj.name + " update buff max stack layer");
                BuffManager buffManager = obj.GetComponent<BuffManager>();
                if (buffManager != null)
                {
                    buffManager.UpdateBuffMaxStackLayer(buffTypeId, maxStackLayer);
                }
            }
            //}
        }
    }


    private void UpdateBuffToManager(int buffId)
    {
        BuffData buff = Resources.Load<BuffData>("Data/BuffData/" + buffId);
        if (buff == null)
        {
            Debug.LogError("BuffData with id " + buffId + " does not exist.");
            return;
        }
        Debug.Log("UpdateBuffToManager: " + buff.name);
        //获取buff的activeLayer
        LayerMask layerMask = buff.Layers();
        //查找所有包含buffManager的对象
        foreach (string controller_type in new string[] { "Player", "Enemy", "Tower", "Summor", "Boss" })
        {
            GameObject[] objectsInLayer = GameObject.FindGameObjectsWithTag(controller_type);
            //判定对象是否在buff的activeLayer中
            foreach (GameObject obj in objectsInLayer)
            {
                //查看obj所在的层是否在buff的activeLayer中
                if ((layerMask & (1 << obj.layer)) != 0)
                {
                    BuffManager buffManager = obj.GetComponent<BuffManager>();
                    if (buffManager != null)
                    {
                        buffManager.AddBuff(buff, buff.buffLevel);
                    }
                }
            }
        }


    }

    private void UpdateBuffCoefToManager(int buffTypeId, Dictionary<string, float> buffCoefficient)
    {
        foreach (string controller_type in new string[] { "Player", "Enemy", "Tower", "Summor", "Boss" })
        {
            //TODO: 只判断受影响层的对象
            //if (buff.isLayerActive(controller_type))
            //{
                GameObject[] objectsInLayer = GameObject.FindGameObjectsWithTag(controller_type);
                foreach (GameObject obj in objectsInLayer)
                {

                    BuffManager buffManager = obj.GetComponent<BuffManager>();
                    if (buffManager != null)
                    {
                    buffManager.UpdateBuffWithCoef(buffTypeId, buffCoefficient);
                }
                }
            //}
        }
    }

 

    public void UpdateBuffPoolToManager(AttributeProperty controller)
    {
        //当新物体创建时，获取所有buff
        string controller_type;
        if (controller is PlayerController)
        {
            controller_type = "Player";
        }
        else if (controller is Enemy)
        {
            controller_type = "Enemy";
        }
        else if (controller is BaseTower)
        {
            controller_type = "Tower";
        }
        else
        {
            controller_type = "Summor";
        }
        // 遍历levelupBuffPool，根据buff的activeLayer筛选buff
        
        foreach (KeyValuePair<int, BuffData> entry in levelupBuffPool)
        {
            int buffId = entry.Key;
            BuffData buff = entry.Value;
            int layerNum = buff.buffLevel;
            if (buff.isLayerActive(controller_type))
            {
                BuffManager buffManager = controller.GetComponent<BuffManager>();
                if (buffManager != null)
                {
                    buffManager.AddBuff(buff, layerNum);
                }
            }
        }
    }

    bool isValidFiled(string fieldName,object obj)
    {
        bool flag = false;
        FieldInfo[] fields = obj.GetType().GetFields();
        foreach (FieldInfo field in fields)
        {
            if (field.Name == fieldName)
            {
                flag = true;
                break;
            }
        }
        return flag;
    }

}