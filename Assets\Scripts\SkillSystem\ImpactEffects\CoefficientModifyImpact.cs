using System.Collections;
using System.Collections.Generic;
using UnityEditor.Localization.Plugins.XLIFF.V20;
using UnityEngine;

public class CoefficientModifyImpact: IImpactEffect
{
    //修改指定脚本的系数,需要脚本存在SetChangeAbleProperty方法
    public IEnumerator Execute(SkillDeployer deployer)
    {
        SkillData data = deployer.SkillData;
        //获取脚本名称
        string scriptName = data.scriptName.ToString(); 
        //获取脚本属性名称
        string propertyField = data.propertyField;
        //获取脚本
        System.Type scriptType = System.Type.GetType(scriptName);
        //首先检测释放者身上是否有指定的脚本
        MonoBehaviour script = deployer.owner.GetComponent(scriptType) as MonoBehaviour;
        //如果没有，判定该脚本是否存在于场景中
        if (script == null)
        {
            script = GameObject.FindObjectOfType(scriptType) as MonoBehaviour;
        }
        //如果还是没有，直接返回
        if (script == null)
        {
            Debug.Log("没有找到需要修改系数的脚本");
            return null;
        }
        Debug.Log("找到需要修改系数的脚本" + scriptName);
        
        //获取脚本的SetChangeAbleProperty方法
        System.Reflection.MethodInfo method = script.GetType().GetMethod("SetChangeAbleProperty");
        //如果没有，直接返回
        if (method == null)
        {
            Debug.Log("没有找到SetChangeAbleProperty方法");
            return null;
        }
        //获取系数
        float coefficient = deployer.SkillData.coefficient;
        //调用SetChangeAbleProperty方法
        method.Invoke(script, new object[] { propertyField, coefficient});
        return null;
    }

}
