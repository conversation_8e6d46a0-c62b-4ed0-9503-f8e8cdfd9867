using LevelUpSytem;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class DebugInfo : MonoBehaviour
{
    // Start is called before the first frame update
    [SerializeField] private CharacterLevelUpSytem _LevelUpSystem;
    [SerializeField] private LevelUpManager _LevelUpManager;    
    [SerializeField] private GameObject _Enemy;
    [SerializeField] private ScenceLog _scenceLog;

    [SerializeField] private Spawner _spawner;

    void Start()
    {
        _LevelUpSystem = FindObjectOfType<CharacterLevelUpSytem>();
        _LevelUpManager = LevelUpManager.Instance;
        _scenceLog = ScenceLog.Instance;
        _spawner = Spawner.Instance;
    }

    // Update is called once per frame
    void Update()
    {
        string _DebugInfoText = "";
        if (_scenceLog != null)
        {
            _DebugInfoText += "Game timor:\n";
            _DebugInfoText += _scenceLog.PlayTime + "\n";
            _DebugInfoText += "DPS:\n";
            foreach (var key in _scenceLog.Dps.Keys)
            {
                _DebugInfoText += key.ToString() + ":" + _scenceLog.Dps[key] + "\n";
            }
        }

        Enemy _EnemyScript = FindObjectOfType<Enemy>();
        //如果Enemy为空，说明还没有生成Enemy，直接返回
        if (_EnemyScript == null)
        {
            _DebugInfoText += "No Enemy\n";
        }
        else
        {
            _Enemy = _EnemyScript.gameObject;
        }
        //展示敌人的buff
        _DebugInfoText += "Enemy Buff:\n";
        foreach (var key in _Enemy.GetComponent<BuffManager>().BuffDict.Keys)
        {
            _DebugInfoText += key + ":" + _Enemy.GetComponent<BuffManager>().BuffDict[key].Item3 + "\n";
        }

        //展示tower上的buff
        GameObject[] _Towers = GameObject.FindGameObjectsWithTag("Tower");
        if (_Towers.Length > 0)
        {
            _DebugInfoText += "Tower Buff:\n";
            foreach (var tower in _Towers)
            {
                _DebugInfoText += tower.name + ":\n";
                BuffManager _buffManager = tower.GetComponent<BuffManager>();
                if (_buffManager != null)
                {
                    foreach (var key in _buffManager.BuffDict.Keys)
                    {
                        _DebugInfoText += key + ":" + _buffManager.BuffDict[key].Item3 + "\n";
                    }
                }
            }
        }

        GetComponent<Text>().text = _DebugInfoText;
    }
}
