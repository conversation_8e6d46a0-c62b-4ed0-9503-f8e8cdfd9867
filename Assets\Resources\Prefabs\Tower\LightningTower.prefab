%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &2951624852069647096
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2951624852069647074}
  - component: {fileID: 2951624852069647101}
  - component: {fileID: 2951624852069647100}
  - component: {fileID: 2951624852069647103}
  - component: {fileID: 2951624852069647102}
  - component: {fileID: 2951624852069647097}
  - component: {fileID: 6427686476040596530}
  - component: {fileID: -1112670376105998100}
  - component: {fileID: -1200351063960471918}
  m_Layer: 7
  m_Name: LightningTower
  m_TagString: Tower
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2951624852069647074
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2951624852069647096}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 6.847979, y: 2.5173936, z: 0}
  m_LocalScale: {x: 0.1, y: 0.1, z: 1}
  m_Children:
  - {fileID: 6531219132744815307}
  - {fileID: 2951624852858111913}
  m_Father: {fileID: 0}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!212 &2951624852069647101
SpriteRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2951624852069647096}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 10754, guid: 0000000000000000f000000000000000, type: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 0
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: -1875020679
  m_SortingLayer: 10
  m_SortingOrder: 0
  m_Sprite: {fileID: 21300000, guid: 3478c20ed471db34ca4c5b69311caeec, type: 3}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_FlipX: 0
  m_FlipY: 0
  m_DrawMode: 0
  m_Size: {x: 1, y: 1}
  m_AdaptiveModeThreshold: 0.5
  m_SpriteTileMode: 0
  m_WasSpriteAssigned: 1
  m_MaskInteraction: 0
  m_SpriteSortPoint: 0
--- !u!50 &2951624852069647100
Rigidbody2D:
  serializedVersion: 4
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2951624852069647096}
  m_BodyType: 2
  m_Simulated: 1
  m_UseFullKinematicContacts: 0
  m_UseAutoMass: 0
  m_Mass: 1
  m_LinearDrag: 0
  m_AngularDrag: 0.05
  m_GravityScale: 1
  m_Material: {fileID: 0}
  m_Interpolate: 0
  m_SleepingMode: 1
  m_CollisionDetection: 0
  m_Constraints: 0
--- !u!61 &2951624852069647103
BoxCollider2D:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2951624852069647096}
  m_Enabled: 1
  m_Density: 1
  m_Material: {fileID: 0}
  m_IsTrigger: 0
  m_UsedByEffector: 0
  m_UsedByComposite: 0
  m_Offset: {x: 0, y: -0.26129192}
  m_SpriteTilingProperty:
    border: {x: 0, y: 0, z: 0, w: 0}
    pivot: {x: 0.5, y: 0.5}
    oldSize: {x: 5.12, y: 5.12}
    newSize: {x: 1, y: 1}
    adaptiveTilingThreshold: 0.5
    drawMode: 0
    adaptiveTiling: 0
  m_AutoTiling: 0
  serializedVersion: 2
  m_Size: {x: 1, y: 0.47741613}
  m_EdgeRadius: 0
--- !u!114 &2951624852069647102
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2951624852069647096}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 13f808d5911769346a7ce10db8f2ce47, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  pickupRange: 0
  moveSpeed: 0
  evasionRate: 0
  attackSpeed: 1
  attackRange: 1
  attackDamage: 1
  bulletSpeed: 0
  maxHealth: 1000
  timeInvincible: 0.2
  invincibleTimer: 0
  isInvincible: 0
  maxSkillNum: 0
  initActiveSkillCD: 10
  passiveSkillHaste: 0
  activeSkillHaste: 0
  extraPickupRange: 0
  extraEvasionRate: 0
  extraMoveSpeed: 0
  extraAttackRange: 0
  extraAttackSpeed: 0
  extraAttackDamage: 0
  extraBulletSpeed: 0
  healthChangePerSecond: 0
  extraHealth: 0
  extraSkillNum: 0
  expCoefficient: 1
  attackSpeedPct: 0
  attackRangePct: 1
  attackDamagePct: 0
  healthChangePerSecondPct: 1
  takeDamagePct: 1
  moveSpeedPct: 1
  controlStatusPct: 1
  debugInfo: {fileID: 2951624853373975341}
  lightning: {fileID: 3937837732752517827, guid: 30eca0d9fa13b7748bf80f4e0356d258, type: 3}
  maxChainTargets: 3
  lightningDuration: 0.1
  splitRadius: 0.5
--- !u!114 &2951624852069647097
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2951624852069647096}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 96ad0732867c6f14e88049ed3ce88e81, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  characterData: {fileID: 11400000, guid: 27bd1efadc614d248becbf85536fb0fa, type: 2}
--- !u!114 &6427686476040596530
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2951624852069647096}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c8f8b5cbf606b14409f2cacf23e7d706, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &-1112670376105998100
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2951624852069647096}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 590f2d0851e11824985357f14e8802e7, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
--- !u!114 &-1200351063960471918
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2951624852069647096}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 93919e18c928a3b428dd6755217c9670, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  debugInfo: {fileID: 0}
  StorageBuff: {fileID: 0}
  anim: {fileID: 0}
--- !u!1 &2951624852858111912
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2951624852858111913}
  - component: {fileID: 2951624852858111916}
  - component: {fileID: 2951624852858111919}
  - component: {fileID: 2951624852858111918}
  m_Layer: 5
  m_Name: Canvas
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2951624852858111913
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2951624852858111912}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 3.9191597, y: 2.5485485, z: 1}
  m_Children:
  - {fileID: 2951624853373975315}
  m_Father: {fileID: 2951624852069647074}
  m_RootOrder: 1
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0, y: 0}
  m_AnchorMax: {x: 0, y: 0}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 1, y: 1}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!223 &2951624852858111916
Canvas:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2951624852858111912}
  m_Enabled: 1
  serializedVersion: 3
  m_RenderMode: 2
  m_Camera: {fileID: 0}
  m_PlaneDistance: 100
  m_PixelPerfect: 0
  m_ReceivesEvents: 1
  m_OverrideSorting: 0
  m_OverridePixelPerfect: 0
  m_SortingBucketNormalizedSize: 0
  m_AdditionalShaderChannelsFlag: 25
  m_SortingLayerID: -1948343857
  m_SortingOrder: 0
  m_TargetDisplay: 0
--- !u!114 &2951624852858111919
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2951624852858111912}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0cd44c1031e13a943bb63640046fad76, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_UiScaleMode: 0
  m_ReferencePixelsPerUnit: 100
  m_ScaleFactor: 1
  m_ReferenceResolution: {x: 800, y: 600}
  m_ScreenMatchMode: 0
  m_MatchWidthOrHeight: 0
  m_PhysicalUnit: 3
  m_FallbackScreenDPI: 96
  m_DefaultSpriteDPI: 96
  m_DynamicPixelsPerUnit: 1
  m_PresetInfoIsWorld: 1
--- !u!114 &2951624852858111918
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2951624852858111912}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc42784cf147c0c48a680349fa168899, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IgnoreReversedGraphics: 1
  m_BlockingObjects: 0
  m_BlockingMask:
    serializedVersion: 2
    m_Bits: **********
--- !u!1 &2951624853373975340
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2951624853373975315}
  - component: {fileID: 2951624853373975314}
  - component: {fileID: 2951624853373975341}
  m_Layer: 5
  m_Name: healthTextBox
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!224 &2951624853373975315
RectTransform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2951624853373975340}
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.1, y: 0.1, z: 1}
  m_Children: []
  m_Father: {fileID: 2951624852858111913}
  m_RootOrder: 0
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_AnchorMin: {x: 0.5, y: 0.5}
  m_AnchorMax: {x: 0.5, y: 0.5}
  m_AnchoredPosition: {x: 0, y: 0}
  m_SizeDelta: {x: 5, y: 1}
  m_Pivot: {x: 0.5, y: 0.5}
--- !u!222 &2951624853373975314
CanvasRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2951624853373975340}
  m_CullTransparentMesh: 1
--- !u!114 &2951624853373975341
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2951624853373975340}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f4688fdb7df04437aeb418b961361dc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Material: {fileID: 0}
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_RaycastTarget: 1
  m_RaycastPadding: {x: 0, y: 0, z: 0, w: 0}
  m_Maskable: 1
  m_OnCullStateChanged:
    m_PersistentCalls:
      m_Calls: []
  m_text: HP
  m_isRightToLeft: 0
  m_fontAsset: {fileID: 11400000, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_sharedMaterial: {fileID: 2180264, guid: 8f586378b4e144a9851e7b34d9b748ee, type: 2}
  m_fontSharedMaterials: []
  m_fontMaterial: {fileID: 0}
  m_fontMaterials: []
  m_fontColor32:
    serializedVersion: 2
    rgba: **********
  m_fontColor: {r: 1, g: 1, b: 1, a: 1}
  m_enableVertexGradient: 0
  m_colorMode: 3
  m_fontColorGradient:
    topLeft: {r: 1, g: 1, b: 1, a: 1}
    topRight: {r: 1, g: 1, b: 1, a: 1}
    bottomLeft: {r: 1, g: 1, b: 1, a: 1}
    bottomRight: {r: 1, g: 1, b: 1, a: 1}
  m_fontColorGradientPreset: {fileID: 0}
  m_spriteAsset: {fileID: 0}
  m_tintAllSprites: 0
  m_StyleSheet: {fileID: 0}
  m_TextStyleHashCode: -1183493901
  m_overrideHtmlColors: 0
  m_faceColor:
    serializedVersion: 2
    rgba: **********
  m_fontSize: 1
  m_fontSizeBase: 1
  m_fontWeight: 400
  m_enableAutoSizing: 0
  m_fontSizeMin: 18
  m_fontSizeMax: 72
  m_fontStyle: 0
  m_HorizontalAlignment: 2
  m_VerticalAlignment: 512
  m_textAlignment: 65535
  m_characterSpacing: 0
  m_wordSpacing: 0
  m_lineSpacing: 0
  m_lineSpacingMax: 0
  m_paragraphSpacing: 0
  m_charWidthMaxAdj: 0
  m_enableWordWrapping: 1
  m_wordWrappingRatios: 0.4
  m_overflowMode: 0
  m_linkedTextComponent: {fileID: 0}
  parentLinkedComponent: {fileID: 0}
  m_enableKerning: 1
  m_enableExtraPadding: 0
  checkPaddingRequired: 0
  m_isRichText: 1
  m_parseCtrlCharacters: 1
  m_isOrthographic: 1
  m_isCullingEnabled: 0
  m_horizontalMapping: 0
  m_verticalMapping: 0
  m_uvLineOffset: 0
  m_geometrySortingOrder: 0
  m_IsTextObjectScaleStatic: 0
  m_VertexBufferAutoSizeReduction: 0
  m_useMaxVisibleDescender: 1
  m_pageToDisplay: 1
  m_margin: {x: 0, y: 0, z: 0, w: 0}
  m_isUsingLegacyAnimationComponent: 0
  m_isVolumetricText: 0
  m_hasFontAssetChanged: 0
  m_baseMaterial: {fileID: 0}
  m_maskOffset: {x: 0, y: 0, z: 0, w: 0}
--- !u!1001 &403530432979545175
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 2951624852069647074}
    m_Modifications:
    - target: {fileID: 6861847529532915356, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
      propertyPath: m_RootOrder
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6861847529532915356, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
      propertyPath: m_LocalScale.x
      value: 1.2760524
      objectReference: {fileID: 0}
    - target: {fileID: 6861847529532915356, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
      propertyPath: m_LocalScale.y
      value: 1.2989111
      objectReference: {fileID: 0}
    - target: {fileID: 6861847529532915356, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
      propertyPath: m_LocalScale.z
      value: 1.4942806
      objectReference: {fileID: 0}
    - target: {fileID: 6861847529532915356, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.14
      objectReference: {fileID: 0}
    - target: {fileID: 6861847529532915356, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.16
      objectReference: {fileID: 0}
    - target: {fileID: 6861847529532915356, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6861847529532915356, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 6861847529532915356, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6861847529532915356, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6861847529532915356, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6861847529532915356, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6861847529532915356, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6861847529532915356, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6861847529532915358, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
      propertyPath: m_Name
      value: electricball
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
--- !u!4 &6531219132744815307 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 6861847529532915356, guid: 139aadff89cbdb04f915d89df218e75f, type: 3}
  m_PrefabInstance: {fileID: 403530432979545175}
  m_PrefabAsset: {fileID: 0}
