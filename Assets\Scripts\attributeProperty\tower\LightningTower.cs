using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DigitalRuby.LightningBolt;

public class LightningTower : BaseTower
{
    public LightningBolt lightning;
    public int maxChainTargets = 3;
    public float lightningDuration;
    public float splitRadius = 0.5f;

    private ScenceLog _scenceLog;
    // Start is called before the first frame update
    protected override void Start()
    {
        //BaseTower.Start](notion://basetower.start/)
        base.Start();
        _scenceLog = ScenceLog.Instance;
       
    }
    // Update is called once per frame
    protected override void Update()
    {
        base.Update();
        if (_scenceLog != null)
        {
            maxChainTargets = (int)_scenceLog.ScenceStats["MaxChainTargets"];
        }
    }

    protected override void Attack(Collider2D[] enemys)
{
    _attackGap -= Time.deltaTime;
    if (_attackGap <= 0)
    {
        for (int i = 0; i < enemys.Length; i++)
        {
            Enemy enemy = enemys[i].GetComponent<Enemy>();
            if (enemy != null)
            {
                StartCoroutine(LaunchLightning(enemy));
                break;
            }
        }
        _attackGap = 1 / attackSpeed;
    }
}

IEnumerator LaunchLightning(Enemy enemy)
{
    //创建闪电实例
    LightningBolt _lightning = Instantiate(lightning);
    _lightning.name = "baseLightning" + Random.Range(0, 1000).ToString();
    _lightning.StartObject = gameObject;

    if (!enemy)
    {
        //print(_lightning.name + "has no target enemy, destroy");

        Destroy(_lightning.gameObject);
        yield break;
    }

    _lightning.EndObject = enemy.gameObject;
    //print(_lightning.name + " targeting " + _lightning.EndObject.transform.position);

    //分裂子闪电
    //yield return StartCoroutine(SplitLightning(_lightning, splitRadius, maxChainTargets));
    SplitLightning(_lightning, splitRadius, maxChainTargets);

    //造成伤害
    yield return new WaitForSeconds(0.1f);
    enemy.ChangeHealth(-1);

    //销毁闪电
    yield return StartCoroutine(DestroyLightning(_lightning, lightningDuration));
}
void SplitLightning(LightningBolt baseLightning, float splitRadius, int chainCount)
{

    if (chainCount == 0)
    {
        return;
    }

    Collider2D[] results = new Collider2D[10];
    int count = Physics2D.OverlapCircleNonAlloc(baseLightning.EndObject.transform.position, splitRadius, results);

    if (count == 0)
    {
        return;
    }

    List<Enemy> targets = new List<Enemy>();
    for (int i = 0; i < count; i++)
    {
        Enemy enemy = results[i].GetComponent<Enemy>();
        if (enemy != null && enemy.gameObject != baseLightning.EndObject)
        {
            targets.Add(enemy);
        }
    }

    targets.Sort((a, b) =>
    {
        float distanceA = Vector2.Distance(a.transform.position, baseLightning.EndObject.transform.position);
        float distanceB = Vector2.Distance(b.transform.position, baseLightning.EndObject.transform.position);
        return distanceA.CompareTo(distanceB);
    });

    int chainTargets = Mathf.Min(chainCount, targets.Count);

    for (int i = 0; i < chainTargets; i++)
    {

        if (baseLightning.EndObject && targets[i])
        {
            LightningBolt subLightning = Instantiate(lightning);
            subLightning.name = "subLightning" + Random.Range(0, 10000).ToString();
            subLightning.StartObject = baseLightning.EndObject;
            subLightning.EndObject = targets[i].gameObject;
            //print("subligtning:" + subLightning.name + " start: " + subLightning.StartObject.transform.position + " end:" + subLightning.EndObject.transform.position + targets[i].name);
            StartCoroutine(TakeLightningDamage(targets[i], 0.1f));
            StartCoroutine(DestroyLightning(subLightning, lightningDuration));
        }
        new WaitForSeconds(0.05f);
    }
}

IEnumerator DestroyLightning(LightningBolt lightningBolt, float duration)
{

    float startTime = Time.time;
    while (Time.time - startTime < duration)
    {
        if (lightningBolt.EndObject == null || lightningBolt.StartObject == null)
        {
            //if (lightningBolt.StartObject == null)
            //    print(lightningBolt.name + " start object missing, destroy");
            //if (lightningBolt.EndObject == null)
            //    print(lightningBolt.name + " end object missing, destroy");
            Destroy(lightningBolt.gameObject);
            yield break;
        }
        yield return null;
    }
    Destroy(lightningBolt.gameObject);
}
IEnumerator TakeLightningDamage(Enemy enemy, float waitSeconds)
{
    yield return new WaitForSeconds(0.1f);
    enemy.ChangeHealth(-1);
}
}