m_ProjectFiles:
  m_ManifestFileStatus:
    m_FilePath: H:/Works/TS/Packages/manifest.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638744470937737439
    m_Hash: 895723374
  m_LockFileStatus:
    m_FilePath: H:/Works/TS/Packages/packages-lock.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638744471277315967
    m_Hash: 3069580508
m_EmbeddedPackageManifests:
  m_ManifestsStatus: {}
m_PackageAssets:
  m_Packages:
    m_ResolvedPackages:
    - packageId: com.unity.2d.animation@5.2.7
      testable: 0
      isDirectDependency: 1
      version: 5.2.7
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.2d.animation@5.2.7
      assetPath: Packages/com.unity.2d.animation
      name: com.unity.2d.animation
      displayName: 2D Animation
      author:
        name: 
        email: 
        url: 
      category: 2D
      type: asset
      description: 2D Animation provides all the necessary tooling and runtime components
        for skeletal animation using Sprites.
      status: 0
      errors: []
      versions:
        all:
        - 1.0.15-preview.1
        - 1.0.15-preview.2
        - 1.0.15-preview.3
        - 1.0.15-preview.4
        - 1.0.15-preview.5
        - 1.0.16-preview
        - 1.0.16-preview.1
        - 1.0.16-preview.2
        - 2.0.0-preview.1
        - 2.0.0-preview.2
        - 2.0.0-preview.3
        - 2.1.0-preview.1
        - 2.1.0-preview.2
        - 2.1.0-preview.4
        - 2.1.0-preview.5
        - 2.1.0-preview.7
        - 2.2.0-preview.1
        - 2.2.0-preview.4
        - 2.2.0-preview.5
        - 2.2.1-preview.1
        - 2.2.1-preview.2
        - 3.0.2
        - 3.0.3
        - 3.0.4
        - 3.0.5
        - 3.0.6
        - 3.0.8
        - 3.1.0
        - 3.1.1
        - 3.2.1
        - 3.2.2
        - 3.2.3
        - 3.2.4
        - 3.2.5
        - 3.2.6
        - 3.2.9
        - 3.2.10
        - 3.2.11
        - 3.2.13
        - 3.2.14
        - 3.2.15
        - 3.2.16
        - 3.2.17
        - 3.2.18
        - 4.0.0
        - 4.0.1
        - 4.1.0
        - 4.1.1
        - 4.2.1
        - 4.2.2
        - 4.2.3
        - 4.2.4
        - 4.2.5
        - 4.2.6
        - 4.2.8
        - 5.0.0
        - 5.0.1
        - 5.0.2
        - 5.0.3
        - 5.0.4
        - 5.0.5
        - 5.0.6
        - 5.0.7
        - 5.0.8
        - 5.0.9
        - 5.0.10
        - 5.1.0
        - 5.1.1
        - 5.2.0
        - 5.2.1
        - 5.2.3
        - 5.2.4
        - 5.2.6
        - 5.2.7
        - 6.0.0-pre.1
        - 6.0.0-pre.2
        - 6.0.1
        - 6.0.3
        - 6.0.4
        - 6.0.5
        - 6.0.7
        - 7.0.0-pre.2
        - 7.0.0-pre.3
        - 7.0.0
        - 7.0.1
        - 7.0.2
        - 7.0.3
        - 7.0.4
        - 7.0.5
        - 7.0.6
        - 7.0.7
        - 7.0.8
        - 7.0.9
        - 7.0.10
        - 7.0.11
        - 7.0.12
        - 7.0.13
        - 7.1.0
        - 7.1.1
        - 7.1.2
        - 7.2.0
        - 8.0.0-pre.3
        - 8.0.0-pre.4
        - 8.0.0
        - 8.0.1
        - 8.0.2
        - 8.0.3
        - 8.0.4
        - 8.0.5
        - 9.0.0-pre.1
        - 9.0.0-pre.3
        - 9.0.0
        - 9.0.1
        - 9.0.2
        - 9.0.3
        - 9.0.4
        - 9.1.0
        - 9.1.1
        - 9.1.2
        - 9.1.3
        - 9.2.0
        - 9.2.1
        - 10.0.0-pre.1
        - 10.0.0-pre.2
        - 10.0.0
        - 10.0.1
        - 10.0.2
        - 10.0.3
        - 10.1.0
        - 10.1.1
        - 10.1.2
        - 10.1.3
        - 10.1.4
        - 10.2.0
        - 10.2.1
        - 11.0.0
        - 12.0.0
        - 12.0.1
        - 12.0.2
        - 13.0.0
        compatible:
        - 5.2.0
        - 5.2.1
        - 5.2.3
        - 5.2.4
        - 5.2.6
        - 5.2.7
        verified: 5.2.7
      dependencies:
      - name: com.unity.2d.common
        version: 4.2.1
      - name: com.unity.2d.sprite
        version: 1.0.0
      - name: com.unity.mathematics
        version: 1.1.0
      - name: com.unity.modules.animation
        version: 1.0.0
      - name: com.unity.modules.uielements
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.2d.common
        version: 4.2.1
      - name: com.unity.2d.sprite
        version: 1.0.0
      - name: com.unity.modules.uielements
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.uielementsnative
        version: 1.0.0
      - name: com.unity.mathematics
        version: 1.1.0
      - name: com.unity.modules.animation
        version: 1.0.0
      keywords:
      - 2d
      - animation
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638179396900000000
      documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.animation@5.2/manual/index.html
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/2d.git
        revision: aa2a8793fe6f8a4b07feb9f612a1ddfd1602413e
        path: 
    - packageId: com.unity.2d.pixel-perfect@4.0.1
      testable: 0
      isDirectDependency: 1
      version: 4.0.1
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.2d.pixel-perfect@4.0.1
      assetPath: Packages/com.unity.2d.pixel-perfect
      name: com.unity.2d.pixel-perfect
      displayName: 2D Pixel Perfect
      author:
        name: 
        email: 
        url: 
      category: 
      type: tool
      description: 'The 2D Pixel Perfect package contains the Pixel Perfect Camera
        component which ensures your pixel art remains crisp and clear at different
        resolutions, and stable in motion.


        It is a single component that
        makes all the calculations needed to scale the viewport with resolution changes,
        removing the hassle from the user. The user can adjust the definition of
        the pixel art rendered within the camera viewport through the component settings,
        as well preview any changes immediately in Game view by using the Run in
        Edit Mode feature.'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0-preview
        - 1.0.1-preview
        - 2.0.2
        - 2.0.3
        - 2.0.4
        - 2.1.0
        - 3.0.0
        - 3.0.1
        - 3.0.2
        - 4.0.0
        - 4.0.1
        - 5.0.0-pre.1
        - 5.0.0-pre.2
        - 5.0.0
        - 5.0.1
        - 5.0.2
        - 5.0.3
        - 5.1.0
        compatible:
        - 4.0.1
        verified: 4.0.1
      dependencies: []
      resolvedDependencies: []
      keywords:
      - pixel
      - perfect
      - 2D
      - sprite
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637328350910000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/2d.git
        revision: 7d7470f4ca0ea8353d4c9640b3b6ae9d1ebd9c1d
        path: 
    - packageId: com.unity.2d.psdimporter@4.3.1
      testable: 0
      isDirectDependency: 1
      version: 4.3.1
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.2d.psdimporter@4.3.1
      assetPath: Packages/com.unity.2d.psdimporter
      name: com.unity.2d.psdimporter
      displayName: 2D PSD Importer
      author:
        name: 
        email: 
        url: 
      category: 2D
      type: asset
      description: A ScriptedImporter for importing Adobe Photoshop PSB (Photoshop
        Big) file format. The ScriptedImporter is currently targeted for users who
        wants to create multi Sprite character animation using Unity 2D Animation
        Package.
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0-preview.1
        - 1.0.0-preview.2
        - 1.0.0-preview.3
        - 1.0.0-preview.4
        - 1.1.0-preview.1
        - 1.1.0-preview.3
        - 1.2.0-preview.1
        - 1.2.0-preview.3
        - 1.2.0-preview.4
        - 2.0.2
        - 2.0.3
        - 2.0.4
        - 2.0.5
        - 2.0.7
        - 2.1.0
        - 2.1.3
        - 2.1.4
        - 2.1.5
        - 2.1.6
        - 2.1.8
        - 2.1.9
        - 2.1.10
        - 2.1.11
        - 3.0.0
        - 3.1.1
        - 3.1.3
        - 3.1.4
        - 3.1.5
        - 3.1.6
        - 3.1.7
        - 4.0.0
        - 4.0.1
        - 4.0.2
        - 4.1.0
        - 4.1.1
        - 4.1.2
        - 4.1.3
        - 4.2.0
        - 4.3.0
        - 4.3.1
        - 5.0.0-pre.1
        - 5.0.0-pre.2
        - 5.0.1
        - 5.0.3
        - 5.0.4
        - 5.0.6
        - 6.0.0-pre.2
        - 6.0.0-pre.3
        - 6.0.0-pre.4
        - 6.0.0
        - 6.0.1
        - 6.0.2
        - 6.0.3
        - 6.0.4
        - 6.0.5
        - 6.0.6
        - 6.0.7
        - 6.0.8
        - 6.0.9
        - 6.1.0
        - 7.0.0-pre.3
        - 7.0.0-pre.4
        - 7.0.0
        - 7.0.1
        - 7.0.2
        - 7.0.3
        - 8.0.0-pre.1
        - 8.0.0-pre.3
        - 8.0.0
        - 8.0.1
        - 8.0.2
        - 8.0.3
        - 8.0.4
        - 8.0.5
        - 8.1.0
        - 8.1.1
        - 9.0.0-pre.1
        - 9.0.0-pre.2
        - 9.0.0
        - 9.0.1
        - 9.0.2
        - 9.0.3
        - 9.1.0
        - 10.0.0
        - 10.1.0
        - 10.1.1
        - 11.0.0
        - 11.0.1
        - 12.0.0
        compatible:
        - 4.3.0
        - 4.3.1
        verified: 4.3.1
      dependencies:
      - name: com.unity.2d.common
        version: 4.2.1
      - name: com.unity.2d.sprite
        version: 1.0.0
      - name: com.unity.2d.animation
        version: 5.2.4
      resolvedDependencies:
      - name: com.unity.2d.common
        version: 4.2.1
      - name: com.unity.2d.sprite
        version: 1.0.0
      - name: com.unity.modules.uielements
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.uielementsnative
        version: 1.0.0
      - name: com.unity.2d.animation
        version: 5.2.7
      - name: com.unity.mathematics
        version: 1.1.0
      - name: com.unity.modules.animation
        version: 1.0.0
      keywords:
      - 2d
      - psdimporter
      - assetimporter
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637988301060000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/2d.git
        revision: c016f0f10972606d85ccfbf9de2033ad5384d6c4
        path: 
    - packageId: com.unity.2d.sprite@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.2d.sprite@1.0.0
      assetPath: Packages/com.unity.2d.sprite
      name: com.unity.2d.sprite
      displayName: 2D Sprite
      author:
        name: 
        email: 
        url: 
      category: 2D
      type: 
      description: Use Unity Sprite Editor Window to create and edit Sprite asset
        properties like pivot, borders and Physics shape
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords:
      - 2d
      - sprite
      - sprite editor window
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.2d.spriteshape@5.3.0
      testable: 0
      isDirectDependency: 1
      version: 5.3.0
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.2d.spriteshape@5.3.0
      assetPath: Packages/com.unity.2d.spriteshape
      name: com.unity.2d.spriteshape
      displayName: 2D SpriteShape
      author:
        name: 
        email: 
        url: 
      category: 2D
      type: asset
      description: SpriteShape Runtime & Editor Package contains the tooling and
        the runtime component that allows you to create very organic looking spline
        based 2D worlds. It comes with intuitive configurator and a highly performant
        renderer.
      status: 0
      errors: []
      versions:
        all:
        - 1.0.10-preview.2
        - 1.0.11-preview
        - 1.0.12-preview
        - 1.0.12-preview.1
        - 1.0.14-preview.2
        - 2.0.0-preview.4
        - 2.0.0-preview.5
        - 2.0.0-preview.7
        - 2.0.0-preview.8
        - 2.0.0-preview.9
        - 2.1.0-preview.2
        - 2.1.0-preview.6
        - 2.1.0-preview.7
        - 2.1.0-preview.10
        - 2.1.0-preview.11
        - 3.0.1
        - 3.0.2
        - 3.0.4
        - 3.0.5
        - 3.0.6
        - 3.0.7
        - 3.0.8
        - 3.0.9
        - 3.0.10
        - 3.0.11
        - 3.0.12
        - 3.0.13
        - 3.0.14
        - 3.0.15
        - 3.0.16
        - 3.0.17
        - 3.0.18
        - 4.0.0
        - 4.0.1
        - 4.0.2
        - 4.0.3
        - 4.1.0
        - 4.1.1
        - 4.1.2
        - 4.1.3
        - 4.1.4
        - 4.1.5
        - 5.0.0
        - 5.0.1
        - 5.0.2
        - 5.1.0
        - 5.1.1
        - 5.1.2
        - 5.1.3
        - 5.1.4
        - 5.1.5
        - 5.1.6
        - 5.1.7
        - 5.2.0
        - 5.3.0
        - 6.0.0-pre.1
        - 6.0.0-pre.2
        - 6.0.0
        - 6.0.1
        - 6.0.2
        - 7.0.0-pre.2
        - 7.0.0-pre.3
        - 7.0.0
        - 7.0.2
        - 7.0.3
        - 7.0.4
        - 7.0.5
        - 7.0.6
        - 7.0.7
        - 7.1.0
        - 8.0.0-pre.4
        - 8.0.0-pre.5
        - 8.0.0
        - 8.0.1
        - 9.0.0-pre.1
        - 9.0.0
        - 9.0.1
        - 9.0.2
        - 9.0.3
        - 9.0.4
        - 9.0.5
        - 9.1.0
        - 9.1.1
        - 10.0.0-pre.1
        - 10.0.0-pre.2
        - 10.0.0
        - 10.0.1
        - 10.0.2
        - 10.0.3
        - 10.0.4
        - 10.0.5
        - 10.0.6
        - 10.0.7
        - 10.1.0
        - 11.0.0
        - 12.0.0
        - 12.0.1
        - 13.0.0
        compatible:
        - 5.3.0
        verified: 5.3.0
      dependencies:
      - name: com.unity.2d.path
        version: 4.0.2
      - name: com.unity.2d.common
        version: 4.2.0
      - name: com.unity.mathematics
        version: 1.1.0
      - name: com.unity.modules.physics2d
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.2d.path
        version: 4.0.2
      - name: com.unity.2d.common
        version: 4.2.1
      - name: com.unity.2d.sprite
        version: 1.0.0
      - name: com.unity.modules.uielements
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.uielementsnative
        version: 1.0.0
      - name: com.unity.mathematics
        version: 1.1.0
      - name: com.unity.modules.physics2d
        version: 1.0.0
      keywords:
      - 2d
      - shape
      - spriteshape
      - smartsprite
      - spline
      - terrain2d
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637836454590000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/2d.git
        revision: de062e9b52e01d9b7c1e2b7e86c8897a78342167
        path: 
    - packageId: com.unity.2d.tilemap@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.2d.tilemap@1.0.0
      assetPath: Packages/com.unity.2d.tilemap
      name: com.unity.2d.tilemap
      displayName: 2D Tilemap Editor
      author:
        name: 
        email: 
        url: 
      category: 2D
      type: 
      description: 2D Tilemap Editor is a package that contains editor functionalities
        for editing Tilemaps.
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords:
      - 2d
      - Tilemap
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.cinemachine@2.6.17
      testable: 0
      isDirectDependency: 1
      version: 2.6.17
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.cinemachine@2.6.17
      assetPath: Packages/com.unity.cinemachine
      name: com.unity.cinemachine
      displayName: Cinemachine
      author:
        name: 
        email: 
        url: 
      category: cinematography
      type: asset
      description: "Smart camera tools for passionate creators. \n\nIMPORTANT NOTE:
        If you are upgrading from the Asset Store version of Cinemachine, delete
        the Cinemachine asset from your project BEFORE installing this version from
        the Package Manager."
      status: 0
      errors: []
      versions:
        all:
        - 2.1.11-beta.1
        - 2.1.12
        - 2.1.13
        - 2.2.0
        - 2.2.7
        - 2.2.8
        - 2.2.9
        - 2.2.10-preview.3
        - 2.2.10-preview.4
        - 2.3.1
        - 2.3.3
        - 2.3.4
        - 2.3.5-preview.3
        - 2.4.0-preview.3
        - 2.4.0-preview.4
        - 2.4.0-preview.6
        - 2.4.0-preview.7
        - 2.4.0-preview.8
        - 2.4.0-preview.9
        - 2.4.0-preview.10
        - 2.4.0
        - 2.5.0
        - 2.6.0-preview.2
        - 2.6.0-preview.3
        - 2.6.0-preview.5
        - 2.6.0-preview.8
        - 2.6.0
        - 2.6.1-preview.6
        - 2.6.1
        - 2.6.2-preview.1
        - 2.6.2
        - 2.6.3-preview.2
        - 2.6.3
        - 2.6.4
        - 2.6.5
        - 2.6.9
        - 2.6.10
        - 2.6.11
        - 2.6.14
        - 2.6.15
        - 2.6.17
        - 2.7.1
        - 2.7.2
        - 2.7.3
        - 2.7.4
        - 2.7.5
        - 2.7.8
        - 2.7.9
        - 2.8.0-exp.1
        - 2.8.0-exp.2
        - 2.8.0-pre.1
        - 2.8.0
        - 2.8.1
        - 2.8.2
        - 2.8.3
        - 2.8.4
        - 2.8.6
        - 2.8.9
        - 2.9.0-pre.1
        - 2.9.0-pre.6
        - 2.9.1
        - 2.9.2
        - 2.9.4
        - 2.9.5
        - 2.9.7
        - 2.10.0
        - 2.10.1
        - 2.10.2
        - 2.10.3
        - 2.10.4
        - 3.0.0-pre.3
        - 3.0.0-pre.4
        - 3.0.0-pre.5
        - 3.0.0-pre.6
        - 3.0.0-pre.7
        - 3.0.0-pre.8
        - 3.0.0-pre.9
        - 3.0.1
        - 3.1.0
        - 3.1.1
        - 3.1.2
        - 3.1.3
        - 3.1.4
        compatible:
        - 2.5.0
        - 2.6.0-preview.2
        - 2.6.0-preview.3
        - 2.6.0-preview.5
        - 2.6.0-preview.8
        - 2.6.0
        - 2.6.1-preview.6
        - 2.6.1
        - 2.6.2-preview.1
        - 2.6.2
        - 2.6.3-preview.2
        - 2.6.3
        - 2.6.4
        - 2.6.5
        - 2.6.9
        - 2.6.10
        - 2.6.11
        - 2.6.14
        - 2.6.15
        - 2.6.17
        - 2.7.1
        - 2.7.2
        - 2.7.3
        - 2.7.4
        - 2.7.5
        - 2.7.8
        - 2.7.9
        - 2.8.0-exp.1
        - 2.8.0-exp.2
        - 2.8.0-pre.1
        - 2.8.0
        - 2.8.1
        - 2.8.2
        - 2.8.3
        - 2.8.4
        - 2.8.6
        - 2.8.9
        - 2.9.0-pre.1
        - 2.9.0-pre.6
        - 2.9.1
        - 2.9.2
        - 2.9.4
        - 2.9.5
        - 2.9.7
        - 2.10.0
        - 2.10.1
        - 2.10.2
        - 2.10.3
        verified: 2.6.17
      dependencies: []
      resolvedDependencies: []
      keywords:
      - camera
      - follow
      - rig
      - fps
      - cinematography
      - aim
      - orbit
      - cutscene
      - cinematic
      - collision
      - freelook
      - cinemachine
      - compose
      - composition
      - dolly
      - track
      - clearshot
      - noise
      - framing
      - handheld
      - lens
      - impulse
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637975495960000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.com/Unity-Technologies/com.unity.cinemachine.git
        revision: 57501b59e2a5755e26dc54181b2b097af0045a44
        path: 
    - packageId: com.unity.collab-proxy@2.1.0
      testable: 0
      isDirectDependency: 1
      version: 2.1.0
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.collab-proxy@2.1.0
      assetPath: Packages/com.unity.collab-proxy
      name: com.unity.collab-proxy
      displayName: Version Control
      author:
        name: 
        email: 
        url: 
      category: Editor
      type: assets
      description: The package gives you the ability to use Unity Version Control
        in the Unity editor. To use Unity Version Control, a subscription is required.
        Learn more about how you can get started for free by visiting plasticscm.com.
      status: 0
      errors: []
      versions:
        all:
        - 1.2.3-preview
        - 1.2.4-preview
        - 1.2.6
        - 1.2.7
        - 1.2.9
        - 1.2.11
        - 1.2.15
        - 1.2.16
        - 1.2.17-preview.3
        - 1.3.2
        - 1.3.3
        - 1.3.4
        - 1.3.5
        - 1.3.6
        - 1.3.7
        - 1.3.8
        - 1.3.9
        - 1.4.9
        - 1.5.7
        - 1.6.0
        - 1.7.1
        - 1.8.0
        - 1.9.0
        - 1.10.2
        - 1.11.2
        - 1.12.5
        - 1.13.5
        - 1.14.1
        - 1.14.4
        - 1.14.7
        - 1.14.9
        - 1.14.12
        - 1.14.13
        - 1.14.15
        - 1.14.16
        - 1.14.17
        - 1.14.18
        - 1.15.1
        - 1.15.4
        - 1.15.7
        - 1.15.9
        - 1.15.12
        - 1.15.13
        - 1.15.15
        - 1.15.16
        - 1.15.17
        - 1.15.18
        - 1.17.0
        - 1.17.1
        - 1.17.2
        - 1.17.6
        - 1.17.7
        - 2.0.0-preview.6
        - 2.0.0-preview.8
        - 2.0.0-preview.15
        - 2.0.0-preview.17
        - 2.0.0-preview.20
        - 2.0.0-preview.21
        - 2.0.0-preview.22
        - 2.0.0
        - 2.0.1
        - 2.0.3
        - 2.0.4
        - 2.0.5
        - 2.0.7
        - 2.1.0-preview.3
        - 2.1.0-preview.5
        - 2.1.0-preview.6
        - 2.1.0
        - 2.2.0
        - 2.3.1
        - 2.4.3
        - 2.4.4
        - 2.5.1
        - 2.5.2
        - 2.6.0
        - 2.7.1
        - 2.8.1
        - 2.8.2
        - 2.9.1
        - 2.9.2
        compatible:
        - 1.17.6
        - 1.17.7
        - 2.0.0-preview.6
        - 2.0.0-preview.8
        - 2.0.0-preview.15
        - 2.0.0-preview.17
        - 2.0.0-preview.20
        - 2.0.0-preview.21
        - 2.0.0-preview.22
        - 2.0.0
        - 2.0.1
        - 2.0.3
        - 2.0.4
        - 2.0.5
        - 2.1.0-preview.3
        - 2.1.0-preview.5
        - 2.1.0-preview.6
        verified: 1.17.7
      dependencies: []
      resolvedDependencies: []
      keywords:
      - backup
      - cloud
      - collab
      - collaborate
      - collaboration
      - control
      - devops
      - plastic
      - plasticscm
      - source
      - team
      - teams
      - version
      - vcs
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638301329890000000
      documentationUrl: https://docs.unity3d.com/Packages/com.unity.collab-proxy@2.1/manual/index.html
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/com.unity.cloud.collaborate.git
        revision: 179e96acc2f95fcb9dcc91b5fb2ee5f3e85d48aa
        path: 
    - packageId: com.unity.ide.rider@3.0.16
      testable: 0
      isDirectDependency: 1
      version: 3.0.16
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.ide.rider@3.0.16
      assetPath: Packages/com.unity.ide.rider
      name: com.unity.ide.rider
      displayName: JetBrains Rider Editor
      author:
        name: 
        email: 
        url: 
      category: 
      type: asset
      description: The JetBrains Rider Editor package provides an integration for
        using the JetBrains Rider IDE as a code editor for Unity. It adds support
        for generating .csproj files for code completion and auto-discovery of installations.
      status: 0
      errors: []
      versions:
        all:
        - 1.0.2
        - 1.0.6
        - 1.0.8
        - 1.1.0
        - 1.1.1
        - 1.1.2-preview
        - 1.1.2-preview.2
        - 1.1.3-preview.1
        - 1.1.4-preview
        - 1.1.4
        - 1.2.0-preview
        - 1.2.1
        - 2.0.0-preview
        - 2.0.1
        - 2.0.2
        - 2.0.3
        - 2.0.5
        - 2.0.7
        - 3.0.1
        - 3.0.2
        - 3.0.3
        - 3.0.4
        - 3.0.5
        - 3.0.6
        - 3.0.7
        - 3.0.9
        - 3.0.10
        - 3.0.12
        - 3.0.13
        - 3.0.14
        - 3.0.15
        - 3.0.16
        - 3.0.17
        - 3.0.18
        - 3.0.20
        - 3.0.21
        - 3.0.22
        - 3.0.24
        - 3.0.25
        - 3.0.26
        - 3.0.27
        - 3.0.28
        - 3.0.31
        - 3.0.34
        - 3.0.35
        - 3.0.36
        compatible:
        - 1.0.2
        - 1.0.6
        - 1.0.8
        - 1.1.0
        - 1.1.1
        - 1.1.2-preview
        - 1.1.2-preview.2
        - 1.1.3-preview.1
        - 1.1.4-preview
        - 1.1.4
        - 1.2.0-preview
        - 1.2.1
        - 2.0.0-preview
        - 2.0.1
        - 2.0.2
        - 2.0.3
        - 2.0.5
        - 2.0.7
        - 3.0.1
        - 3.0.2
        - 3.0.3
        - 3.0.4
        - 3.0.5
        - 3.0.6
        - 3.0.7
        - 3.0.9
        - 3.0.10
        - 3.0.12
        - 3.0.13
        - 3.0.14
        - 3.0.15
        - 3.0.16
        - 3.0.17
        - 3.0.18
        - 3.0.20
        - 3.0.21
        - 3.0.22
        - 3.0.24
        - 3.0.25
        - 3.0.26
        - 3.0.27
        - 3.0.28
        - 3.0.31
        - 3.0.34
        - 3.0.35
        - 3.0.36
        verified: 3.0.36
      dependencies:
      - name: com.unity.ext.nunit
        version: 1.0.6
      resolvedDependencies:
      - name: com.unity.ext.nunit
        version: 1.0.6
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638011833630000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.rider.git
        revision: 9b718d58b8f7a34e564f2c79af6d2ca788e929d4
        path: 
    - packageId: com.unity.ide.visualstudio@2.0.22
      testable: 0
      isDirectDependency: 1
      version: 2.0.22
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.ide.visualstudio@2.0.22
      assetPath: Packages/com.unity.ide.visualstudio
      name: com.unity.ide.visualstudio
      displayName: Visual Studio Editor
      author:
        name: 
        email: 
        url: 
      category: 
      type: assets
      description: Code editor integration for supporting Visual Studio as code editor
        for unity. Adds support for generating csproj files for intellisense purposes,
        auto discovery of installations, etc.
      status: 0
      errors: []
      versions:
        all:
        - 1.0.2
        - 1.0.3
        - 1.0.4
        - 1.0.9
        - 1.0.10
        - 1.0.11
        - 2.0.0
        - 2.0.1
        - 2.0.2
        - 2.0.3
        - 2.0.5
        - 2.0.7
        - 2.0.8
        - 2.0.9
        - 2.0.11
        - 2.0.12
        - 2.0.14
        - 2.0.15
        - 2.0.16
        - 2.0.17
        - 2.0.18
        - 2.0.20
        - 2.0.21
        - 2.0.22
        - 2.0.23
        compatible:
        - 1.0.2
        - 1.0.3
        - 1.0.4
        - 1.0.9
        - 1.0.10
        - 1.0.11
        - 2.0.0
        - 2.0.1
        - 2.0.2
        - 2.0.3
        - 2.0.5
        - 2.0.7
        - 2.0.8
        - 2.0.9
        - 2.0.11
        - 2.0.12
        - 2.0.14
        - 2.0.15
        - 2.0.16
        - 2.0.17
        - 2.0.18
        - 2.0.20
        - 2.0.21
        - 2.0.22
        - 2.0.23
        verified: 2.0.23
      dependencies:
      - name: com.unity.test-framework
        version: 1.1.9
      resolvedDependencies:
      - name: com.unity.test-framework
        version: 1.1.33
      - name: com.unity.ext.nunit
        version: 1.0.6
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638326061910000000
      documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.visualstudio@2.0/manual/index.html
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.visualstudio.git
        revision: 700b44077345e97d37d464ff25507638983aed64
        path: 
    - packageId: com.unity.ide.vscode@1.2.5
      testable: 0
      isDirectDependency: 1
      version: 1.2.5
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.ide.vscode@1.2.5
      assetPath: Packages/com.unity.ide.vscode
      name: com.unity.ide.vscode
      displayName: Visual Studio Code Editor
      author:
        name: 
        email: 
        url: 
      category: 
      type: asset
      description: Code editor integration for supporting Visual Studio Code as code
        editor for unity. Adds support for generating csproj files for intellisense
        purposes, auto discovery of installations, etc.
      status: 0
      errors: []
      versions:
        all:
        - 1.0.2
        - 1.0.3
        - 1.0.7
        - 1.1.0
        - 1.1.2
        - 1.1.3
        - 1.1.4
        - 1.2.0
        - 1.2.1
        - 1.2.2
        - 1.2.3
        - 1.2.4
        - 1.2.5
        compatible:
        - 1.0.2
        - 1.0.3
        - 1.0.7
        - 1.1.0
        - 1.1.2
        - 1.1.3
        - 1.1.4
        - 1.2.0
        - 1.2.1
        - 1.2.2
        - 1.2.3
        - 1.2.4
        - 1.2.5
        verified: 1.2.5
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637800255360000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.com/Unity-Technologies/com.unity.ide.vscode.git
        revision: b0740c80bfc2440527c317109f7c3d9100132722
        path: 
    - packageId: com.unity.inputsystem@1.8.1
      testable: 0
      isDirectDependency: 1
      version: 1.8.1
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.inputsystem@1.8.1
      assetPath: Packages/com.unity.inputsystem
      name: com.unity.inputsystem
      displayName: Input System
      author:
        name: 
        email: 
        url: 
      category: 
      type: assets
      description: A new input system which can be used as a more extensible and
        customizable alternative to Unity's classic input system in UnityEngine.Input.
      status: 0
      errors: []
      versions:
        all:
        - 0.1.2-preview
        - 0.2.0-preview
        - 0.2.1-preview
        - 0.2.6-preview
        - 0.2.8-preview
        - 0.2.10-preview
        - 0.9.0-preview
        - 0.9.1-preview
        - 0.9.2-preview
        - 0.9.3-preview
        - 0.9.4-preview
        - 0.9.5-preview
        - 0.9.6-preview
        - 1.0.0-preview
        - 1.0.0-preview.1
        - 1.0.0-preview.2
        - 1.0.0-preview.3
        - 1.0.0-preview.4
        - 1.0.0-preview.5
        - 1.0.0-preview.6
        - 1.0.0-preview.7
        - 1.0.0
        - 1.0.1
        - 1.0.2
        - 1.1.0-pre.5
        - 1.1.0-pre.6
        - 1.1.0-preview.1
        - 1.1.0-preview.2
        - 1.1.0-preview.3
        - 1.1.1
        - 1.2.0
        - 1.3.0
        - 1.4.1
        - 1.4.2
        - 1.4.3
        - 1.4.4
        - 1.5.0
        - 1.5.1
        - 1.6.1
        - 1.6.3
        - 1.7.0
        - 1.8.0-pre.1
        - 1.8.0-pre.2
        - 1.8.0
        - 1.8.1
        - 1.8.2
        - 1.9.0
        - 1.10.0
        - 1.11.0
        - 1.11.1
        - 1.11.2
        - 1.12.0
        - 1.13.0
        - 1.13.1
        - 1.14.0
        - 1.14.1
        - 1.14.2
        compatible:
        - 0.1.2-preview
        - 0.2.0-preview
        - 0.2.1-preview
        - 0.2.6-preview
        - 0.2.8-preview
        - 0.2.10-preview
        - 0.9.0-preview
        - 0.9.1-preview
        - 0.9.2-preview
        - 0.9.3-preview
        - 0.9.4-preview
        - 0.9.5-preview
        - 0.9.6-preview
        - 1.0.0-preview
        - 1.0.0-preview.1
        - 1.0.0-preview.2
        - 1.0.0-preview.3
        - 1.0.0-preview.4
        - 1.0.0-preview.5
        - 1.0.0-preview.6
        - 1.0.0-preview.7
        - 1.0.0
        - 1.0.1
        - 1.0.2
        - 1.1.0-pre.5
        - 1.1.0-pre.6
        - 1.1.0-preview.1
        - 1.1.0-preview.2
        - 1.1.0-preview.3
        - 1.1.1
        - 1.2.0
        - 1.3.0
        - 1.4.1
        - 1.4.2
        - 1.4.3
        - 1.4.4
        - 1.5.0
        - 1.5.1
        - 1.6.1
        - 1.6.3
        - 1.7.0
        - 1.8.0-pre.1
        - 1.8.0-pre.2
        - 1.8.0
        - 1.8.1
        - 1.8.2
        - 1.9.0
        - 1.10.0
        - 1.11.0
        - 1.11.1
        - 1.11.2
        - 1.12.0
        verified: 1.4.4
      dependencies:
      - name: com.unity.modules.uielements
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.uielements
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.uielementsnative
        version: 1.0.0
      keywords:
      - input
      - events
      - keyboard
      - mouse
      - gamepad
      - touch
      - vr
      - xr
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638460930440000000
      documentationUrl: https://docs.unity3d.com/Packages/com.unity.inputsystem@1.8/manual/index.html
      hasRepository: 1
      repository:
        type: git
        url: https://github.com/Unity-Technologies/InputSystem.git
        revision: 4412578426d34b7b2a0ef17db295e39e7e26b2fa
        path: 
    - packageId: com.unity.localization@1.4.5
      testable: 0
      isDirectDependency: 1
      version: 1.4.5
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.localization@1.4.5
      assetPath: Packages/com.unity.localization
      name: com.unity.localization
      displayName: Localization
      author:
        name: 
        email: 
        url: 
      category: 
      type: assets
      description: "Use the Localization package to easily configure localization
        settings for your application.\n\nAdd support for multiple languages and
        regional variants, including:\n\n\u2022 String localization: Set different
        strings to display based on locale. Use the Smart Strings feature to add
        logic to automatically replace specific strings, such as placeholders and
        plurals.\n\u2022 Asset localization: Use a different asset (such as a texture,
        model, or audio file) based on a locale.\n\u2022 Pseudo-localization: Test
        how your project will adapt to different localizations at an early stage,
        before adding your translations.\n\u2022 Import and export localization data
        to XLIFF, CSV and Google Sheets.\n\nAdd localization to your projects using
        the Localization package to help make your applications more accessible to
        a wider audience."
      status: 0
      errors: []
      versions:
        all:
        - 0.2.1-preview
        - 0.2.2-preview
        - 0.2.3-preview
        - 0.3.1-preview
        - 0.3.2-preview
        - 0.4.0-preview
        - 0.5.0-preview
        - 0.5.1-preview
        - 0.6.0-preview
        - 0.6.1-preview
        - 0.7.1-preview
        - 0.8.0-preview
        - 0.8.1-preview
        - 0.9.0-preview
        - 0.10.0-preview
        - 0.11.0-preview
        - 0.11.1-preview
        - 1.0.0-pre.8
        - 1.0.0-pre.9
        - 1.0.0-pre.10
        - 1.0.0
        - 1.0.1
        - 1.0.2
        - 1.0.3
        - 1.0.4
        - 1.0.5
        - 1.1.0
        - 1.1.1
        - 1.2.1
        - 1.3.1
        - 1.3.2
        - 1.4.0-exp.1
        - 1.4.2
        - 1.4.3
        - 1.4.4
        - 1.4.5
        - 1.5.0-pre.2
        - 1.5.0-pre.3
        - 1.5.0-pre.4
        - 1.5.0-pre.5
        - 1.5.0-pre.6
        - 1.5.0-pre.7
        - 1.5.1
        - 1.5.2
        - 1.5.3
        - 1.5.4
        - 1.5.5
        - 1.5.7
        compatible:
        - 0.2.1-preview
        - 0.2.2-preview
        - 0.2.3-preview
        - 0.3.1-preview
        - 0.3.2-preview
        - 0.4.0-preview
        - 0.5.0-preview
        - 0.5.1-preview
        - 0.6.0-preview
        - 0.6.1-preview
        - 0.7.1-preview
        - 0.8.0-preview
        - 0.8.1-preview
        - 0.9.0-preview
        - 0.10.0-preview
        - 0.11.0-preview
        - 0.11.1-preview
        - 1.0.0-pre.8
        - 1.0.0-pre.9
        - 1.0.0-pre.10
        - 1.0.0
        - 1.0.1
        - 1.0.2
        - 1.0.3
        - 1.0.4
        - 1.0.5
        - 1.1.0
        - 1.1.1
        - 1.2.1
        - 1.3.1
        - 1.3.2
        - 1.4.0-exp.1
        - 1.4.2
        - 1.4.3
        - 1.4.4
        - 1.4.5
        - 1.5.0-pre.2
        - 1.5.0-pre.3
        - 1.5.0-pre.4
        - 1.5.0-pre.5
        - 1.5.0-pre.6
        - 1.5.0-pre.7
        - 1.5.1
        - 1.5.2
        - 1.5.3
        - 1.5.4
        - 1.5.5
        - 1.5.7
        verified: 
      dependencies:
      - name: com.unity.addressables
        version: 1.21.8
      - name: com.unity.nuget.newtonsoft-json
        version: 3.0.2
      resolvedDependencies:
      - name: com.unity.addressables
        version: 1.21.8
      - name: com.unity.modules.assetbundle
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.imageconversion
        version: 1.0.0
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.scriptablebuildpipeline
        version: 1.21.3
      - name: com.unity.modules.unitywebrequestassetbundle
        version: 1.0.0
      - name: com.unity.nuget.newtonsoft-json
        version: 3.0.2
      keywords:
      - localization
      - locale
      - language
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638308256820000000
      documentationUrl: https://docs.unity3d.com/Packages/com.unity.localization@1.4/manual/index.html
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/com.unity.localization.git
        revision: a4890be557c52504bb1c494b8b425c4c4efb61ad
        path: 
    - packageId: com.unity.render-pipelines.high-definition@10.10.1
      testable: 0
      isDirectDependency: 1
      version: 10.10.1
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.high-definition@10.10.1
      assetPath: Packages/com.unity.render-pipelines.high-definition
      name: com.unity.render-pipelines.high-definition
      displayName: High Definition RP
      author:
        name: 
        email: 
        url: 
      category: 
      type: asset
      description: The High Definition Render Pipeline (HDRP) is a high-fidelity
        Scriptable Render Pipeline built by Unity to target modern (Compute Shader
        compatible) platforms. HDRP utilizes Physically-Based Lighting techniques,
        linear lighting, HDR lighting, and a configurable hybrid Tile/Cluster deferred/Forward
        lighting architecture and gives you the tools you need to create games, technical
        demos, animations, and more to a high graphical standard.
      status: 0
      errors: []
      versions:
        all:
        - 0.1.21
        - 0.1.27
        - 0.1.28
        - 1.0.0-beta
        - 1.0.1-beta
        - 1.1.1-preview
        - 1.1.2-preview
        - 1.1.4-preview
        - 1.1.5-preview
        - 1.1.8-preview
        - 1.1.10-preview
        - 1.1.11-preview
        - 2.0.1-preview
        - 2.0.3-preview
        - 2.0.4-preview
        - 2.0.4-preview.1
        - 2.0.5-preview
        - 2.0.6-preview
        - 2.0.7-preview
        - 2.0.8-preview
        - 3.0.0-preview
        - 3.1.0-preview
        - 3.3.0-preview
        - 4.0.0-preview
        - 4.0.1-preview
        - 4.1.0-preview
        - 4.2.0-preview
        - 4.3.0-preview
        - 4.6.0-preview
        - 4.8.0-preview
        - 4.9.0-preview
        - 4.10.0-preview
        - 5.0.0-preview
        - 5.1.0-preview
        - 5.2.0-preview
        - 5.2.1-preview
        - 5.2.2-preview
        - 5.2.3-preview
        - 5.3.1-preview
        - 5.6.1-preview
        - 5.7.2-preview
        - 5.8.2-preview
        - 5.9.0-preview
        - 5.10.0-preview
        - 5.13.0-preview
        - 5.16.1-preview
        - 6.5.2-preview
        - 6.5.3-preview
        - 6.7.1-preview
        - 6.9.0-preview
        - 6.9.1-preview
        - 6.9.2-preview
        - 7.0.0
        - 7.0.1
        - 7.1.1
        - 7.1.2
        - 7.1.5
        - 7.1.6
        - 7.1.7
        - 7.1.8
        - 7.2.0
        - 7.2.1
        - 7.3.1
        - 7.4.1
        - 7.4.2
        - 7.4.3
        - 7.5.1
        - 7.5.2
        - 7.5.3
        - 7.6.0
        - 7.7.0
        - 7.7.1
        - 8.0.1
        - 8.1.0
        - 8.2.0
        - 8.3.1
        - 9.0.0-preview.13
        - 9.0.0-preview.33
        - 9.0.0-preview.54
        - 9.0.0-preview.71
        - 10.0.0-preview.27
        - 10.1.0
        - 10.2.0
        - 10.2.1
        - 10.2.2
        - 10.3.1
        - 10.3.2
        - 10.4.0
        - 10.5.0
        - 10.5.1
        - 10.6.0
        - 10.7.0
        - 10.8.0
        - 10.8.1
        - 10.9.0
        - 10.10.0
        - 10.10.1
        compatible:
        - 10.10.1
        verified: 10.10.1
      dependencies:
      - name: com.unity.shadergraph
        version: 10.10.1
      - name: com.unity.visualeffectgraph
        version: 10.10.1
      - name: com.unity.render-pipelines.core
        version: 10.10.1
      - name: com.unity.render-pipelines.high-definition-config
        version: 10.10.1
      resolvedDependencies:
      - name: com.unity.shadergraph
        version: 10.10.1
      - name: com.unity.searcher
        version: 4.3.2
      - name: com.unity.render-pipelines.core
        version: 10.10.1
      - name: com.unity.ugui
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.visualeffectgraph
        version: 10.10.1
      - name: com.unity.render-pipelines.high-definition-config
        version: 10.10.1
      keywords:
      - graphics
      - performance
      - rendering
      - render
      - pipeline
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638012630740000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.com/Unity-Technologies/Graphics.git
        revision: 625fae22af5330cedb9096076d8504e74c3c3ea5
        path: 
    - packageId: com.unity.render-pipelines.high-definition-config@file:H:\Works\TS\LocalPackages\com.unity.render-pipelines.high-definition-config
      testable: 0
      isDirectDependency: 1
      version: 10.10.1
      source: 4
      resolvedPath: H:\Works\TS\LocalPackages\com.unity.render-pipelines.high-definition-config
      assetPath: Packages/com.unity.render-pipelines.high-definition-config
      name: com.unity.render-pipelines.high-definition-config
      displayName: High Definition RP Config
      author:
        name: 
        email: 
        url: 
      category: 
      type: 
      description: Configuration files for the High Definition Render Pipeline.
      status: 0
      errors: []
      versions:
        all:
        - 7.0.0
        - 7.0.1
        - 7.1.1
        - 7.1.2
        - 7.1.5
        - 7.1.6
        - 7.1.7
        - 7.1.8
        - 7.2.0
        - 7.2.1
        - 7.3.1
        - 7.4.1
        - 7.4.2
        - 7.4.3
        - 7.5.1
        - 7.5.2
        - 7.5.3
        - 7.6.0
        - 7.7.0
        - 7.7.1
        - 8.0.1
        - 8.1.0
        - 8.2.0
        - 8.3.1
        - 9.0.0-preview.15
        - 9.0.0-preview.35
        - 9.0.0-preview.57
        - 9.0.0-preview.74
        - 10.0.0-preview.27
        - 10.1.0
        - 10.2.0
        - 10.2.1
        - 10.2.2
        - 10.3.1
        - 10.3.2
        - 10.4.0
        - 10.5.0
        - 10.5.1
        - 10.6.0
        - 10.7.0
        - 10.8.0
        - 10.8.1
        - 10.9.0
        - 10.10.0
        - 10.10.1
        compatible:
        - 10.10.1
        verified: 10.10.1
      dependencies:
      - name: com.unity.render-pipelines.core
        version: 10.10.1
      resolvedDependencies:
      - name: com.unity.render-pipelines.core
        version: 10.10.1
      - name: com.unity.ugui
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.com/Unity-Technologies/Graphics.git
        revision: 625fae22af5330cedb9096076d8504e74c3c3ea5
        path: 
    - packageId: com.unity.render-pipelines.universal@10.10.1
      testable: 0
      isDirectDependency: 1
      version: 10.10.1
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.universal@10.10.1
      assetPath: Packages/com.unity.render-pipelines.universal
      name: com.unity.render-pipelines.universal
      displayName: Universal RP
      author:
        name: 
        email: 
        url: 
      category: 
      type: asset
      description: The Universal Render Pipeline (URP) is a prebuilt Scriptable Render
        Pipeline, made by Unity. URP provides artist-friendly workflows that let
        you quickly and easily create optimized graphics across a range of platforms,
        from mobile to high-end consoles and PCs.
      status: 0
      errors: []
      versions:
        all:
        - 7.0.0
        - 7.0.1
        - 7.1.1
        - 7.1.2
        - 7.1.5
        - 7.1.6
        - 7.1.7
        - 7.1.8
        - 7.2.0
        - 7.2.1
        - 7.3.1
        - 7.4.1
        - 7.4.2
        - 7.4.3
        - 7.5.1
        - 7.5.2
        - 7.5.3
        - 7.6.0
        - 7.7.0
        - 7.7.1
        - 8.0.1
        - 8.1.0
        - 8.2.0
        - 8.3.1
        - 9.0.0-preview.14
        - 9.0.0-preview.35
        - 9.0.0-preview.55
        - 9.0.0-preview.72
        - 10.0.0-preview.26
        - 10.1.0
        - 10.2.0
        - 10.2.1
        - 10.2.2
        - 10.3.1
        - 10.3.2
        - 10.4.0
        - 10.5.0
        - 10.5.1
        - 10.6.0
        - 10.7.0
        - 10.8.0
        - 10.8.1
        - 10.9.0
        - 10.10.0
        - 10.10.1
        compatible:
        - 10.10.1
        verified: 10.10.1
      dependencies:
      - name: com.unity.mathematics
        version: 1.1.0
      - name: com.unity.shadergraph
        version: 10.10.1
      - name: com.unity.render-pipelines.core
        version: 10.10.1
      resolvedDependencies:
      - name: com.unity.mathematics
        version: 1.1.0
      - name: com.unity.shadergraph
        version: 10.10.1
      - name: com.unity.searcher
        version: 4.3.2
      - name: com.unity.render-pipelines.core
        version: 10.10.1
      - name: com.unity.ugui
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      keywords:
      - graphics
      - performance
      - rendering
      - mobile
      - render
      - pipeline
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638012630770000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.com/Unity-Technologies/Graphics.git
        revision: 625fae22af5330cedb9096076d8504e74c3c3ea5
        path: 
    - packageId: com.unity.shadergraph@10.10.1
      testable: 0
      isDirectDependency: 1
      version: 10.10.1
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.shadergraph@10.10.1
      assetPath: Packages/com.unity.shadergraph
      name: com.unity.shadergraph
      displayName: Shader Graph
      author:
        name: 
        email: 
        url: 
      category: 
      type: asset
      description: The Shader Graph package adds a visual Shader editing tool to
        Unity. You can use this tool to create Shaders in a visual way instead of
        writing code. Specific render pipelines can implement specific graph features.
        Currently, both the High Definition Rendering Pipeline and the Universal
        Rendering Pipeline support Shader Graph.
      status: 0
      errors: []
      versions:
        all:
        - 0.1.8
        - 0.1.9
        - 0.1.17
        - 1.0.0-beta
        - 1.1.1-preview
        - 1.1.2-preview
        - 1.1.3-preview
        - 1.1.4-preview
        - 1.1.5-preview
        - 1.1.6-preview
        - 1.1.8-preview
        - 1.1.9-preview
        - 2.0.1-preview
        - 2.0.3-preview
        - 2.0.4-preview
        - 2.0.4-preview.1
        - 2.0.5-preview
        - 2.0.6-preview
        - 2.0.7-preview
        - 2.0.8-preview
        - 3.0.0-preview
        - 3.1.0-preview
        - 3.3.0-preview
        - 4.0.0-preview
        - 4.0.1-preview
        - 4.1.0-preview
        - 4.2.0-preview
        - 4.3.0-preview
        - 4.6.0-preview
        - 4.8.0-preview
        - 4.9.0-preview
        - 4.10.0-preview
        - 5.0.0-preview
        - 5.1.0
        - 5.2.0
        - 5.2.1
        - 5.2.2
        - 5.2.3
        - 5.3.1
        - 5.6.1
        - 5.7.2
        - 5.8.2
        - 5.9.0
        - 5.10.0
        - 5.13.0
        - 5.16.1
        - 6.5.2
        - 6.5.3
        - 6.7.1
        - 6.9.0
        - 6.9.1
        - 6.9.2
        - 7.0.0
        - 7.0.1
        - 7.1.1
        - 7.1.2
        - 7.1.5
        - 7.1.6
        - 7.1.7
        - 7.1.8
        - 7.2.0
        - 7.2.1
        - 7.3.1
        - 7.4.1
        - 7.4.2
        - 7.4.3
        - 7.5.1
        - 7.5.2
        - 7.5.3
        - 7.6.0
        - 7.7.0
        - 7.7.1
        - 8.0.1
        - 8.1.0
        - 8.2.0
        - 8.3.1
        - 9.0.0-preview.14
        - 9.0.0-preview.34
        - 9.0.0-preview.55
        - 9.0.0-preview.72
        - 10.0.0-preview.27
        - 10.1.0
        - 10.2.0
        - 10.2.1
        - 10.2.2
        - 10.3.1
        - 10.3.2
        - 10.4.0
        - 10.5.0
        - 10.5.1
        - 10.6.0
        - 10.7.0
        - 10.8.0
        - 10.8.1
        - 10.9.0
        - 10.10.0
        - 10.10.1
        compatible:
        - 10.10.1
        verified: 10.10.1
      dependencies:
      - name: com.unity.searcher
        version: 4.3.2
      - name: com.unity.render-pipelines.core
        version: 10.10.1
      resolvedDependencies:
      - name: com.unity.searcher
        version: 4.3.2
      - name: com.unity.render-pipelines.core
        version: 10.10.1
      - name: com.unity.ugui
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638012630530000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.com/Unity-Technologies/Graphics.git
        revision: 625fae22af5330cedb9096076d8504e74c3c3ea5
        path: 
    - packageId: com.unity.test-framework@1.1.33
      testable: 0
      isDirectDependency: 1
      version: 1.1.33
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.test-framework@1.1.33
      assetPath: Packages/com.unity.test-framework
      name: com.unity.test-framework
      displayName: Test Framework
      author:
        name: 
        email: 
        url: 
      category: Unity Test Framework
      type: asset
      description: Test framework for running Edit mode and Play mode tests in Unity.
      status: 0
      errors: []
      versions:
        all:
        - 0.0.4-preview
        - 0.0.29-preview
        - 1.0.0
        - 1.0.7
        - 1.0.9
        - 1.0.11
        - 1.0.12
        - 1.0.13
        - 1.0.14
        - 1.0.16
        - 1.0.17
        - 1.0.18
        - 1.1.0
        - 1.1.1
        - 1.1.2
        - 1.1.3
        - 1.1.5
        - 1.1.8
        - 1.1.9
        - 1.1.11
        - 1.1.13
        - 1.1.14
        - 1.1.16
        - 1.1.18
        - 1.1.19
        - 1.1.20
        - 1.1.22
        - 1.1.24
        - 1.1.26
        - 1.1.27
        - 1.1.29
        - 1.1.30
        - 1.1.31
        - 1.1.33
        - 1.3.0
        - 1.3.1
        - 1.3.2
        - 1.3.3
        - 1.3.4
        - 1.3.5
        - 1.3.7
        - 1.3.8
        - 1.3.9
        - 1.4.0
        - 1.4.1
        - 1.4.2
        - 1.4.3
        - 1.4.4
        - 1.4.5
        - 1.4.6
        - 2.0.1-exp.1
        - 2.0.1-exp.2
        - 2.0.1-pre.12
        - 2.0.1-pre.18
        compatible:
        - 1.1.33
        - 1.3.0
        - 1.3.1
        - 1.3.2
        - 1.3.3
        - 1.3.4
        - 1.3.5
        - 1.3.7
        - 1.3.8
        - 1.3.9
        - 1.4.0
        - 1.4.1
        - 1.4.2
        - 1.4.3
        - 1.4.4
        - 1.4.5
        - 1.4.6
        - 2.0.1-exp.1
        - 2.0.1-exp.2
        - 2.0.1-pre.12
        - 2.0.1-pre.18
        verified: 1.1.33
      dependencies:
      - name: com.unity.ext.nunit
        version: 1.0.6
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.ext.nunit
        version: 1.0.6
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      keywords:
      - Test
      - TestFramework
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637938212700000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.com/Unity-Technologies/com.unity.test-framework.git
        revision: 34a4d423d64926635eb36d3bb86276179cc186e1
        path: 
    - packageId: com.unity.textmeshpro@3.0.9
      testable: 0
      isDirectDependency: 1
      version: 3.0.9
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.textmeshpro@3.0.9
      assetPath: Packages/com.unity.textmeshpro
      name: com.unity.textmeshpro
      displayName: TextMeshPro
      author:
        name: 
        email: 
        url: 
      category: Text Rendering
      type: assets
      description: 'TextMeshPro is the ultimate text solution for Unity. It''s the
        perfect replacement for Unity''s UI Text and the legacy Text Mesh.


        Powerful
        and easy to use, TextMeshPro (also known as TMP) uses Advanced Text Rendering
        techniques along with a set of custom shaders; delivering substantial visual
        quality improvements while giving users incredible flexibility when it comes
        to text styling and texturing.


        TextMeshPro provides Improved Control
        over text formatting and layout with features like character, word, line
        and paragraph spacing, kerning, justified text, Links, over 30 Rich Text
        Tags available, support for Multi Font & Sprites, Custom Styles and more.


        Great
        performance. Since the geometry created by TextMeshPro uses two triangles
        per character just like Unity''s text components, this improved visual quality
        and flexibility comes at no additional performance cost.'
      status: 0
      errors: []
      versions:
        all:
        - 0.1.2
        - 1.0.21
        - 1.0.23
        - 1.0.25
        - 1.0.26
        - 1.1.0
        - 1.2.0
        - 1.2.1
        - 1.2.2
        - 1.2.3
        - 1.2.4
        - 1.3.0-preview
        - 1.3.0
        - 1.4.0-preview.1b
        - 1.4.0-preview.2a
        - 1.4.0-preview.3a
        - 1.4.0
        - 1.4.1-preview.1
        - 1.4.1
        - 1.5.0-preview.1
        - 1.5.0-preview.2
        - 1.5.0-preview.3
        - 1.5.0-preview.4
        - 1.5.0-preview.5
        - 1.5.0-preview.6
        - 1.5.0-preview.7
        - 1.5.0-preview.8
        - 1.5.0-preview.10
        - 1.5.0-preview.11
        - 1.5.0-preview.12
        - 1.5.0-preview.13
        - 1.5.0-preview.14
        - 1.5.0
        - 1.5.1
        - 1.5.3
        - 1.5.4
        - 1.5.5
        - 1.5.6
        - 1.6.0-preview.1
        - 2.0.0
        - 2.0.1-preview.1
        - 2.0.1
        - 2.1.0-preview.1
        - 2.1.0-preview.2
        - 2.1.0-preview.3
        - 2.1.0-preview.4
        - 2.1.0-preview.5
        - 2.1.0-preview.7
        - 2.1.0-preview.8
        - 2.1.0-preview.10
        - 2.1.0-preview.11
        - 2.1.0-preview.12
        - 2.1.0-preview.13
        - 2.1.0-preview.14
        - 2.1.0
        - 2.1.1
        - 2.1.3
        - 2.1.4
        - 2.1.5
        - 2.1.6
        - 2.2.0-preview.1
        - 2.2.0-preview.2
        - 2.2.0-preview.3
        - 3.0.0-preview.1
        - 3.0.0-preview.3
        - 3.0.0-preview.4
        - 3.0.0-preview.5
        - 3.0.0-preview.7
        - 3.0.0-preview.8
        - 3.0.0-preview.10
        - 3.0.0-preview.11
        - 3.0.0-preview.12
        - 3.0.0-preview.13
        - 3.0.0-preview.14
        - 3.0.0
        - 3.0.1
        - 3.0.3
        - 3.0.4
        - 3.0.5
        - 3.0.6
        - 3.0.7
        - 3.0.8
        - 3.0.9
        - 3.2.0-pre.1
        - 3.2.0-pre.2
        - 3.2.0-pre.3
        - 3.2.0-pre.4
        - 3.2.0-pre.5
        - 3.2.0-pre.6
        - 3.2.0-pre.7
        - 3.2.0-pre.8
        - 3.2.0-pre.9
        - 3.2.0-pre.10
        - 3.2.0-pre.11
        - 3.2.0-pre.12
        - 3.2.0-pre.13
        - 4.0.0-pre.1
        - 4.0.0-pre.2
        compatible:
        - 3.0.6
        - 3.0.7
        - 3.0.8
        - 3.0.9
        - 3.2.0-pre.1
        - 3.2.0-pre.2
        - 3.2.0-pre.3
        - 3.2.0-pre.4
        - 3.2.0-pre.5
        - 3.2.0-pre.6
        - 3.2.0-pre.7
        - 3.2.0-pre.8
        - 3.2.0-pre.9
        - 3.2.0-pre.10
        verified: 3.0.9
      dependencies:
      - name: com.unity.ugui
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.ugui
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      keywords:
      - TextMeshPro
      - TextMesh Pro
      - TMP
      - Text
      - SDF
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638515173480000000
      documentationUrl: https://docs.unity3d.com/Packages/com.unity.textmeshpro@3.0/manual/index.html
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/com.unity.textmeshpro.git
        revision: 0af193626b4f76795f7cb6c9b0f55389d0e4b1d6
        path: 
    - packageId: com.unity.timeline@1.4.8
      testable: 0
      isDirectDependency: 1
      version: 1.4.8
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.timeline@1.4.8
      assetPath: Packages/com.unity.timeline
      name: com.unity.timeline
      displayName: Timeline
      author:
        name: 
        email: 
        url: 
      category: 
      type: asset
      description: Use Unity Timeline to create cinematic content, game-play sequences,
        audio sequences, and complex particle effects.
      status: 0
      errors: []
      versions:
        all:
        - 1.2.0
        - 1.2.1
        - 1.2.2
        - 1.2.3
        - 1.2.4
        - 1.2.5
        - 1.2.6
        - 1.2.7
        - 1.2.9
        - 1.2.10
        - 1.2.11
        - 1.2.12
        - 1.2.13
        - 1.2.14
        - 1.2.15
        - 1.2.16
        - 1.2.17
        - 1.2.18
        - 1.3.0-preview.2
        - 1.3.0-preview.3
        - 1.3.0-preview.5
        - 1.3.0-preview.6
        - 1.3.0-preview.7
        - 1.3.0
        - 1.3.1
        - 1.3.2
        - 1.3.3
        - 1.3.4
        - 1.3.5
        - 1.3.6
        - 1.3.7
        - 1.4.0-preview.1
        - 1.4.0-preview.2
        - 1.4.0-preview.3
        - 1.4.0-preview.5
        - 1.4.0-preview.6
        - 1.4.0-preview.7
        - 1.4.0
        - 1.4.1
        - 1.4.2
        - 1.4.3
        - 1.4.4
        - 1.4.5
        - 1.4.6
        - 1.4.7
        - 1.4.8
        - 1.5.0-pre.2
        - 1.5.0-preview.1
        - 1.5.0-preview.2
        - 1.5.0-preview.3
        - 1.5.0-preview.4
        - 1.5.0-preview.5
        - 1.5.1-pre.1
        - 1.5.1-pre.2
        - 1.5.1-pre.3
        - 1.5.2
        - 1.5.4
        - 1.5.5
        - 1.5.6
        - 1.5.7
        - 1.6.0-pre.1
        - 1.6.0-pre.3
        - 1.6.0-pre.4
        - 1.6.0-pre.5
        - 1.6.1
        - 1.6.2
        - 1.6.3
        - 1.6.4
        - 1.6.5
        - 1.7.0-pre.1
        - 1.7.0-pre.2
        - 1.7.0
        - 1.7.1
        - 1.7.2
        - 1.7.3
        - 1.7.4
        - 1.7.5
        - 1.7.6
        - 1.7.7
        - 1.8.0
        - 1.8.1
        - 1.8.2
        - 1.8.3
        - 1.8.4
        - 1.8.5
        - 1.8.6
        - 1.8.7
        - 1.8.8
        - 1.8.9
        compatible:
        - 1.4.8
        - 1.5.0-pre.2
        - 1.5.0-preview.1
        - 1.5.0-preview.2
        - 1.5.0-preview.3
        - 1.5.0-preview.4
        - 1.5.0-preview.5
        - 1.5.1-pre.1
        - 1.5.1-pre.2
        - 1.5.1-pre.3
        - 1.5.2
        - 1.5.4
        - 1.5.5
        - 1.5.6
        - 1.5.7
        - 1.6.0-pre.1
        - 1.6.0-pre.3
        - 1.6.0-pre.4
        - 1.6.0-pre.5
        - 1.6.1
        - 1.6.2
        - 1.6.3
        - 1.6.4
        - 1.6.5
        - 1.7.0-pre.1
        - 1.7.0-pre.2
        - 1.7.0
        - 1.7.1
        - 1.7.2
        - 1.7.3
        - 1.7.4
        - 1.7.5
        - 1.7.6
        - 1.7.7
        - 1.8.0
        - 1.8.1
        - 1.8.2
        - 1.8.3
        - 1.8.4
        - 1.8.5
        - 1.8.6
        - 1.8.7
        verified: 1.4.8
      dependencies:
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.director
        version: 1.0.0
      - name: com.unity.modules.animation
        version: 1.0.0
      - name: com.unity.modules.particlesystem
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.director
        version: 1.0.0
      - name: com.unity.modules.animation
        version: 1.0.0
      - name: com.unity.modules.particlesystem
        version: 1.0.0
      keywords:
      - unity
      - animation
      - editor
      - timeline
      - tools
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637557616100000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/com.unity.timeline.git
        revision: 527e4364e28028f20f04fc51db671eb4bb910a26
        path: 
    - packageId: com.unity.ugui@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.ugui@1.0.0
      assetPath: Packages/com.unity.ugui
      name: com.unity.ugui
      displayName: Unity UI
      author:
        name: 
        email: 
        url: 
      category: 
      type: 
      description: "Unity UI is a UI toolkit for developing user interfaces for games
        and applications. It is a GameObject-based UI system that uses Components
        and the Game View to arrange, position, and style user interfaces. \u200B
        You cannot use Unity UI to create or change user interfaces in the Unity
        Editor."
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        - 3.0.0-exp.1
        - 3.0.0-exp.3
        - 3.0.0-exp.4
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      keywords:
      - UI
      - ugui
      - Unity UI
      - Canvas
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.ai@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.ai@1.0.0
      assetPath: Packages/com.unity.modules.ai
      name: com.unity.modules.ai
      displayName: AI
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The AI module implements the path finding features in Unity.
        Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AIModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.androidjni@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.androidjni@1.0.0
      assetPath: Packages/com.unity.modules.androidjni
      name: com.unity.modules.androidjni
      displayName: Android JNI
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'AndroidJNI module allows you to call Java code. Scripting API:
        https://docs.unity3d.com/ScriptReference/UnityEngine.AndroidJNIModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.animation@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.animation@1.0.0
      assetPath: Packages/com.unity.modules.animation
      name: com.unity.modules.animation
      displayName: Animation
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Animation module implements Unity''s animation system. Scripting
        API: https://docs.unity3d.com/ScriptReference/UnityEngine.AnimationModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.assetbundle@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.assetbundle@1.0.0
      assetPath: Packages/com.unity.modules.assetbundle
      name: com.unity.modules.assetbundle
      displayName: Asset Bundle
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The AssetBundle module implements the AssetBundle class and related
        APIs to load data from AssetBundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AssetBundleModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.audio@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.audio@1.0.0
      assetPath: Packages/com.unity.modules.audio
      name: com.unity.modules.audio
      displayName: Audio
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Audio module implements Unity''s audio system. Scripting
        API: https://docs.unity3d.com/ScriptReference/UnityEngine.AudioModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.cloth@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.cloth@1.0.0
      assetPath: Packages/com.unity.modules.cloth
      name: com.unity.modules.cloth
      displayName: Cloth
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Cloth module implements cloth physics simulation through
        the Cloth component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ClothModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.physics
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.physics
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.director@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.director@1.0.0
      assetPath: Packages/com.unity.modules.director
      name: com.unity.modules.director
      displayName: Director
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Director module implements the PlayableDirector class. Scripting
        API: https://docs.unity3d.com/ScriptReference/UnityEngine.DirectorModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.animation
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.animation
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.imageconversion@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.imageconversion@1.0.0
      assetPath: Packages/com.unity.modules.imageconversion
      name: com.unity.modules.imageconversion
      displayName: Image Conversion
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The ImageConversion module implements the ImageConversion class
        which provides helper methods to convert images from and to PNG, JPEG or
        EXR formats. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ImageConversionModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.imgui@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.imgui@1.0.0
      assetPath: Packages/com.unity.modules.imgui
      name: com.unity.modules.imgui
      displayName: IMGUI
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The IMGUI module provides Unity''s immediate mode GUI solution
        for creating in-game and editor user interfaces. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.IMGUIModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.jsonserialize@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.jsonserialize@1.0.0
      assetPath: Packages/com.unity.modules.jsonserialize
      name: com.unity.modules.jsonserialize
      displayName: JSONSerialize
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The JSONSerialize module provides the JsonUtility class which
        lets you serialize Unity Objects to JSON format. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.JSONSerializeModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.particlesystem@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.particlesystem@1.0.0
      assetPath: Packages/com.unity.modules.particlesystem
      name: com.unity.modules.particlesystem
      displayName: Particle System
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The ParticleSystem module implements Unity''s Particle System.
        Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ParticleSystemModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.physics@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.physics@1.0.0
      assetPath: Packages/com.unity.modules.physics
      name: com.unity.modules.physics
      displayName: Physics
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Physics module implements 3D physics in Unity. Scripting
        API: https://docs.unity3d.com/ScriptReference/UnityEngine.PhysicsModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.physics2d@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.physics2d@1.0.0
      assetPath: Packages/com.unity.modules.physics2d
      name: com.unity.modules.physics2d
      displayName: Physics 2D
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Physics2d module implements 2D physics in Unity. Scripting
        API: https://docs.unity3d.com/ScriptReference/UnityEngine.Physics2DModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.screencapture@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.screencapture@1.0.0
      assetPath: Packages/com.unity.modules.screencapture
      name: com.unity.modules.screencapture
      displayName: Screen Capture
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The ScreenCapture module provides functionality to take screen
        shots using the ScreenCapture class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ScreenCaptureModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.imageconversion
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.imageconversion
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.terrain@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.terrain@1.0.0
      assetPath: Packages/com.unity.modules.terrain
      name: com.unity.modules.terrain
      displayName: Terrain
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Terrain module implements Unity''s Terrain rendering engine
        available through the Terrain component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.terrainphysics@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.terrainphysics@1.0.0
      assetPath: Packages/com.unity.modules.terrainphysics
      name: com.unity.modules.terrainphysics
      displayName: Terrain Physics
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The TerrainPhysics module connects the Terrain and Physics modules
        by implementing the TerrainCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainPhysicsModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.terrain
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.terrain
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.tilemap@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.tilemap@1.0.0
      assetPath: Packages/com.unity.modules.tilemap
      name: com.unity.modules.tilemap
      displayName: Tilemap
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Tilemap module implements the Tilemap class. Scripting API:
        https://docs.unity3d.com/ScriptReference/UnityEngine.TilemapModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.physics2d
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.physics2d
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.ui@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.ui@1.0.0
      assetPath: Packages/com.unity.modules.ui
      name: com.unity.modules.ui
      displayName: UI
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The UI module implements basic components required for Unity''s
        UI system Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.uielements@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.uielements@1.0.0
      assetPath: Packages/com.unity.modules.uielements
      name: com.unity.modules.uielements
      displayName: UIElements
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The UIElements module implements the UIElements retained mode
        UI framework. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIElementsModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.uielementsnative
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.uielementsnative
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.umbra@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.umbra@1.0.0
      assetPath: Packages/com.unity.modules.umbra
      name: com.unity.modules.umbra
      displayName: Umbra
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Umbra module implements Unity''s occlusion culling system.
        Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UmbraModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.unityanalytics@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.unityanalytics@1.0.0
      assetPath: Packages/com.unity.modules.unityanalytics
      name: com.unity.modules.unityanalytics
      displayName: Unity Analytics
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The UnityAnalytics module implements APIs required to use Unity
        Analytics. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityAnalyticsModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.unitywebrequest@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.unitywebrequest@1.0.0
      assetPath: Packages/com.unity.modules.unitywebrequest
      name: com.unity.modules.unitywebrequest
      displayName: Unity Web Request
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The UnityWebRequest module lets you communicate with http services.
        Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.unitywebrequestassetbundle@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.unitywebrequestassetbundle@1.0.0
      assetPath: Packages/com.unity.modules.unitywebrequestassetbundle
      name: com.unity.modules.unitywebrequestassetbundle
      displayName: Unity Web Request Asset Bundle
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The UnityWebRequestAssetBundle module provides the DownloadHandlerAssetBundle
        class to use UnityWebRequest to download Asset Bundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAssetBundleModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.assetbundle
        version: 1.0.0
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.assetbundle
        version: 1.0.0
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.unitywebrequestaudio@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.unitywebrequestaudio@1.0.0
      assetPath: Packages/com.unity.modules.unitywebrequestaudio
      name: com.unity.modules.unitywebrequestaudio
      displayName: Unity Web Request Audio
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The UnityWebRequestAudio module provides the DownloadHandlerAudioClip
        class to use UnityWebRequest to download AudioClips. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAudioModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.audio
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.audio
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.unitywebrequesttexture@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.unitywebrequesttexture@1.0.0
      assetPath: Packages/com.unity.modules.unitywebrequesttexture
      name: com.unity.modules.unitywebrequesttexture
      displayName: Unity Web Request Texture
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The UnityWebRequestTexture module provides the DownloadHandlerTexture
        class to use UnityWebRequest to download Textures. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestTextureModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.imageconversion
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.imageconversion
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.unitywebrequestwww@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.unitywebrequestwww@1.0.0
      assetPath: Packages/com.unity.modules.unitywebrequestwww
      name: com.unity.modules.unitywebrequestwww
      displayName: Unity Web Request WWW
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The UnityWebRequestWWW module implements the legacy WWW lets
        you communicate with http services. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestWWWModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.unitywebrequestassetbundle
        version: 1.0.0
      - name: com.unity.modules.unitywebrequestaudio
        version: 1.0.0
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.assetbundle
        version: 1.0.0
      - name: com.unity.modules.imageconversion
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.modules.unitywebrequestassetbundle
        version: 1.0.0
      - name: com.unity.modules.assetbundle
        version: 1.0.0
      - name: com.unity.modules.unitywebrequestaudio
        version: 1.0.0
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.imageconversion
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.vehicles@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.vehicles@1.0.0
      assetPath: Packages/com.unity.modules.vehicles
      name: com.unity.modules.vehicles
      displayName: Vehicles
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Vehicles module implements vehicle physics simulation through
        the WheelCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VehiclesModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.physics
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.physics
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.video@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.video@1.0.0
      assetPath: Packages/com.unity.modules.video
      name: com.unity.modules.video
      displayName: Video
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Video module lets you play back video files in your content.
        Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VideoModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.audio
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.vr@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.vr@1.0.0
      assetPath: Packages/com.unity.modules.vr
      name: com.unity.modules.vr
      displayName: VR
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The VR module implements support for virtual reality devices
        in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VRModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.xr
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.xr
        version: 1.0.0
      - name: com.unity.modules.subsystems
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.wind@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.wind@1.0.0
      assetPath: Packages/com.unity.modules.wind
      name: com.unity.modules.wind
      displayName: Wind
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Wind module implements the WindZone component which can affect
        terrain rendering and particle simulations. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.WindModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.xr@1.0.0
      testable: 0
      isDirectDependency: 1
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.xr@1.0.0
      assetPath: Packages/com.unity.modules.xr
      name: com.unity.modules.xr
      displayName: XR
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The XR module contains the VR and AR related platform support
        functionality. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.XRModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.subsystems
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.subsystems
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.subsystems@1.0.0
      testable: 0
      isDirectDependency: 0
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.subsystems@1.0.0
      assetPath: Packages/com.unity.modules.subsystems
      name: com.unity.modules.subsystems
      displayName: Subsystems
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 'The Subsystem module contains the definitions and runtime support
        for general subsystems in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.SubsystemsModule.html'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.modules.uielementsnative@1.0.0
      testable: 0
      isDirectDependency: 0
      version: 1.0.0
      source: 2
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.modules.uielementsnative@1.0.0
      assetPath: Packages/com.unity.modules.uielementsnative
      name: com.unity.modules.uielementsnative
      displayName: UIElements Native
      author:
        name: 
        email: 
        url: 
      category: 
      type: module
      description: 
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0
        compatible:
        - 1.0.0
        verified: 1.0.0
      dependencies:
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 0
      documentationUrl: 
      hasRepository: 0
      repository:
        type: 
        url: 
        revision: 
        path: 
    - packageId: com.unity.ext.nunit@1.0.6
      testable: 0
      isDirectDependency: 0
      version: 1.0.6
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.ext.nunit@1.0.6
      assetPath: Packages/com.unity.ext.nunit
      name: com.unity.ext.nunit
      displayName: Custom NUnit
      author:
        name: 
        email: 
        url: 
      category: Libraries
      type: asset
      description: Custom version of the nunit package build to work with Unity.
        Used by the Unity Test Framework.
      status: 0
      errors: []
      versions:
        all:
        - 0.1.5-preview
        - 0.1.6-preview
        - 0.1.9-preview
        - 1.0.0
        - 1.0.5
        - 1.0.6
        - 2.0.2
        - 2.0.3
        - 2.0.4
        - 2.0.5
        compatible:
        - 0.1.5-preview
        - 0.1.6-preview
        - 0.1.9-preview
        - 1.0.0
        - 1.0.5
        - 1.0.6
        - 2.0.2
        - 2.0.3
        - 2.0.4
        - 2.0.5
        verified: 1.0.6
      dependencies: []
      resolvedDependencies: []
      keywords:
      - nunit
      - unittest
      - test
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637429759280000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/com.unity.ext.nunit.git
        revision: 29ea4d6504a5f58fb3a6934db839aa80ae6d9d88
        path: 
    - packageId: com.unity.searcher@4.3.2
      testable: 0
      isDirectDependency: 0
      version: 4.3.2
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.searcher@4.3.2
      assetPath: Packages/com.unity.searcher
      name: com.unity.searcher
      displayName: Searcher
      author:
        name: 
        email: 
        url: 
      category: 
      type: asset
      description: General search window for use in the Editor. First target use
        is for GraphView node search.
      status: 0
      errors: []
      versions:
        all:
        - 4.0.0-preview
        - 4.0.0
        - 4.0.7-preview
        - 4.0.7
        - 4.0.8-preview
        - 4.0.9
        - 4.1.0-preview
        - 4.1.0
        - 4.2.0
        - 4.3.0
        - 4.3.1
        - 4.3.2
        - 4.6.0-preview
        - 4.7.0-preview
        - 4.9.1
        - 4.9.2
        - 4.9.3
        compatible:
        - 4.0.0-preview
        - 4.0.0
        - 4.0.7-preview
        - 4.0.7
        - 4.0.8-preview
        - 4.0.9
        - 4.1.0-preview
        - 4.1.0
        - 4.2.0
        - 4.3.0
        - 4.3.1
        - 4.3.2
        - 4.6.0-preview
        - 4.7.0-preview
        - 4.9.1
        - 4.9.2
        - 4.9.3
        verified: 4.3.2
      dependencies: []
      resolvedDependencies: []
      keywords:
      - search
      - searcher
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637509898070000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/com.unity.searcher.git
        revision: 798bfb84c8f09f7788a3a88c3c16d72c50451530
        path: 
    - packageId: com.unity.render-pipelines.core@10.10.1
      testable: 0
      isDirectDependency: 0
      version: 10.10.1
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.render-pipelines.core@10.10.1
      assetPath: Packages/com.unity.render-pipelines.core
      name: com.unity.render-pipelines.core
      displayName: Core RP Library
      author:
        name: 
        email: 
        url: 
      category: 
      type: asset
      description: SRP Core makes it easier to create or customize a Scriptable Render
        Pipeline (SRP). SRP Core contains reusable code, including boilerplate code
        for working with platform-specific graphics APIs, utility functions for common
        rendering operations, and  shader libraries. The code in SRP Core is use
        by the High Definition Render Pipeline (HDRP) and Universal Render Pipeline
        (URP). If you are creating a custom SRP from scratch or customizing a prebuilt
        SRP, using SRP Core will save you time.
      status: 0
      errors: []
      versions:
        all:
        - 0.1.21
        - 0.1.27
        - 0.1.28
        - 1.0.0-beta
        - 1.0.1-beta
        - 1.1.1-preview
        - 1.1.2-preview
        - 1.1.4-preview
        - 1.1.5-preview
        - 1.1.8-preview
        - 1.1.10-preview
        - 1.1.11-preview
        - 2.0.1-preview
        - 2.0.3-preview
        - 2.0.4-preview
        - 2.0.4-preview.1
        - 2.0.5-preview
        - 2.0.6-preview
        - 2.0.7-preview
        - 2.0.8-preview
        - 3.0.0-preview
        - 3.1.0-preview
        - 3.3.0-preview
        - 4.0.0-preview
        - 4.0.1-preview
        - 4.1.0-preview
        - 4.2.0-preview
        - 4.3.0-preview
        - 4.6.0-preview
        - 4.8.0-preview
        - 4.9.0-preview
        - 4.10.0-preview
        - 5.0.0-preview
        - 5.1.0
        - 5.2.0
        - 5.2.1
        - 5.2.2
        - 5.2.3
        - 5.3.1
        - 5.6.1
        - 5.7.2
        - 5.8.2
        - 5.9.0
        - 5.10.0
        - 5.13.0
        - 5.16.1
        - 6.5.2
        - 6.5.3
        - 6.7.1
        - 6.9.0
        - 6.9.1
        - 6.9.2
        - 7.0.0
        - 7.0.1
        - 7.1.1
        - 7.1.2
        - 7.1.5
        - 7.1.6
        - 7.1.7
        - 7.1.8
        - 7.2.0
        - 7.2.1
        - 7.3.1
        - 7.4.1
        - 7.4.2
        - 7.4.3
        - 7.5.1
        - 7.5.2
        - 7.5.3
        - 7.6.0
        - 7.7.0
        - 7.7.1
        - 8.0.1
        - 8.1.0
        - 8.2.0
        - 8.3.1
        - 9.0.0-preview.13
        - 9.0.0-preview.35
        - 9.0.0-preview.38
        - 9.0.0-preview.60
        - 9.0.0-preview.77
        - 10.0.0-preview.30
        - 10.1.0
        - 10.2.0
        - 10.2.1
        - 10.2.2
        - 10.3.1
        - 10.3.2
        - 10.4.0
        - 10.5.0
        - 10.5.1
        - 10.6.0
        - 10.7.0
        - 10.8.0
        - 10.8.1
        - 10.9.0
        - 10.10.0
        - 10.10.1
        compatible:
        - 10.10.1
        verified: 10.10.1
      dependencies:
      - name: com.unity.ugui
        version: 1.0.0
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.ugui
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638012630510000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.com/Unity-Technologies/Graphics.git
        revision: 625fae22af5330cedb9096076d8504e74c3c3ea5
        path: 
    - packageId: com.unity.mathematics@1.1.0
      testable: 0
      isDirectDependency: 0
      version: 1.1.0
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.mathematics@1.1.0
      assetPath: Packages/com.unity.mathematics
      name: com.unity.mathematics
      displayName: Mathematics
      author:
        name: 
        email: 
        url: 
      category: 
      type: 
      description: Unity's C# SIMD math library providing vector types and math functions
        with a shader like syntax.
      status: 0
      errors: []
      versions:
        all:
        - 0.0.12-preview.2
        - 0.0.12-preview.5
        - 0.0.12-preview.8
        - 0.0.12-preview.10
        - 0.0.12-preview.11
        - 0.0.12-preview.13
        - 0.0.12-preview.17
        - 0.0.12-preview.19
        - 0.0.12-preview.20
        - 1.0.0-preview.1
        - 1.0.1
        - 1.1.0-preview.1
        - 1.1.0
        - 1.2.1
        - 1.2.4
        - 1.2.5
        - 1.2.6
        - 1.3.1
        - 1.3.2
        compatible:
        - 0.0.12-preview.2
        - 0.0.12-preview.5
        - 0.0.12-preview.8
        - 0.0.12-preview.10
        - 0.0.12-preview.11
        - 0.0.12-preview.13
        - 0.0.12-preview.17
        - 0.0.12-preview.19
        - 0.0.12-preview.20
        - 1.0.0-preview.1
        - 1.0.1
        - 1.1.0-preview.1
        - 1.1.0
        - 1.2.1
        - 1.2.4
        - 1.2.5
        - 1.2.6
        - 1.3.1
        verified: 1.2.6
      dependencies: []
      resolvedDependencies: []
      keywords:
      - unity
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 636984649280000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: **************:Unity-Technologies/Unity.Mathematics.git
        revision: 0a3dfafaf606098ea681de4c4be60f3aac63b773
        path: 
    - packageId: com.unity.visualeffectgraph@10.10.1
      testable: 0
      isDirectDependency: 0
      version: 10.10.1
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.visualeffectgraph@10.10.1
      assetPath: Packages/com.unity.visualeffectgraph
      name: com.unity.visualeffectgraph
      displayName: Visual Effect Graph
      author:
        name: 
        email: 
        url: 
      category: 
      type: asset
      description: The Visual Effect Graph is a node based visual effect editor.
        It allows you to author next generation visual effects that Unity simulates
        directly on the GPU. The Visual Effect Graph is production-ready for the
        High Definition Render Pipeline and runs on all platforms supported by it.
        Full support for the Universal Render Pipeline and compatible mobile devices
        is still in development.
      status: 0
      errors: []
      versions:
        all:
        - 4.3.0-preview
        - 4.6.0-preview
        - 4.8.0-preview
        - 4.9.0-preview
        - 4.10.0-preview
        - 5.2.0-preview
        - 5.2.1-preview
        - 5.2.3-preview
        - 5.3.1-preview
        - 5.6.1-preview
        - 5.7.2-preview
        - 5.8.2-preview
        - 5.10.0-preview
        - 5.13.0-preview
        - 5.16.1-preview
        - 6.5.2-preview
        - 6.5.3-preview
        - 6.7.1-preview
        - 6.9.0-preview
        - 6.9.1-preview
        - 6.9.2-preview
        - 7.0.0
        - 7.0.1
        - 7.1.1
        - 7.1.2
        - 7.1.5
        - 7.1.6
        - 7.1.7
        - 7.1.8
        - 7.2.0
        - 7.2.1
        - 7.3.1
        - 7.4.1
        - 7.4.2
        - 7.4.3
        - 7.5.1
        - 7.5.2
        - 7.5.3
        - 7.6.0
        - 7.7.0
        - 7.7.1
        - 8.0.1
        - 8.1.0
        - 8.2.0
        - 8.3.1
        - 9.0.0-preview.13
        - 9.0.0-preview.33
        - 9.0.0-preview.54
        - 9.0.0-preview.71
        - 10.0.0-preview.27
        - 10.1.0
        - 10.2.0
        - 10.2.1
        - 10.2.2
        - 10.3.1
        - 10.3.2
        - 10.4.0
        - 10.5.0
        - 10.5.1
        - 10.6.0
        - 10.7.0
        - 10.8.0
        - 10.8.1
        - 10.9.0
        - 10.10.0
        - 10.10.1
        compatible:
        - 10.10.1
        verified: 10.10.1
      dependencies:
      - name: com.unity.shadergraph
        version: 10.10.1
      - name: com.unity.render-pipelines.core
        version: 10.10.1
      resolvedDependencies:
      - name: com.unity.shadergraph
        version: 10.10.1
      - name: com.unity.searcher
        version: 4.3.2
      - name: com.unity.render-pipelines.core
        version: 10.10.1
      - name: com.unity.ugui
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.physics
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      keywords:
      - vfx
      - visualeffect
      - graph
      - effect
      - particles
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638012630610000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.com/Unity-Technologies/Graphics.git
        revision: 625fae22af5330cedb9096076d8504e74c3c3ea5
        path: 
    - packageId: com.unity.addressables@1.21.8
      testable: 0
      isDirectDependency: 0
      version: 1.21.8
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.addressables@1.21.8
      assetPath: Packages/com.unity.addressables
      name: com.unity.addressables
      displayName: Addressables
      author:
        name: 
        email: 
        url: 
      category: 
      type: asset
      description: 'The Addressable Asset System allows the developer to ask for
        an asset via its address. Once an asset (e.g. a prefab) is marked "addressable",
        it generates an address which can be called from anywhere. Wherever the asset
        resides (local or remote), the system will locate it and its dependencies,
        then return it.


        Use ''Window->Asset Management->Addressables''
        to begin working with the system.


        Addressables use asynchronous
        loading to support loading from any location with any collection of dependencies.
        Whether you have been using direct references, traditional asset bundles,
        or Resource folders, addressables provide a simpler way to make your game
        more dynamic. Addressables simultaneously opens up the world of asset bundles
        while managing all the complexity.


        For usage samples, see github.com/Unity-Technologies/Addressables-Sample'
      status: 0
      errors: []
      versions:
        all:
        - 0.0.8-preview
        - 0.0.12-preview
        - 0.0.15-preview
        - 0.0.16-preview
        - 0.0.18-preview
        - 0.0.22-preview
        - 0.0.26-preview
        - 0.0.27-preview
        - 0.1.2-preview
        - 0.2.1-preview
        - 0.2.2-preview
        - 0.3.5-preview
        - 0.4.6-preview
        - 0.4.8-preview
        - 0.5.2-preview
        - 0.5.3-preview
        - 0.6.6-preview
        - 0.6.7-preview
        - 0.6.8-preview
        - 0.7.4-preview
        - 0.7.5-preview
        - 0.8.4-preview
        - 0.8.6-preview
        - 1.1.3-preview
        - 1.1.4-preview
        - 1.1.5
        - 1.1.7
        - 1.1.9
        - 1.1.10
        - 1.2.2
        - 1.2.3
        - 1.2.4
        - 1.3.3
        - 1.3.8
        - 1.4.0
        - 1.5.0
        - 1.5.1
        - 1.6.0
        - 1.6.2
        - 1.7.4
        - 1.7.5
        - 1.8.3
        - 1.8.4
        - 1.8.5
        - 1.9.2
        - 1.10.0
        - 1.11.2
        - 1.12.0
        - 1.13.1
        - 1.14.2
        - 1.15.1
        - 1.16.1
        - 1.16.6
        - 1.16.7
        - 1.16.8
        - 1.16.10
        - 1.16.12
        - 1.16.13
        - 1.16.15
        - 1.16.16
        - 1.16.19
        - 1.17.0-preview
        - 1.17.2-preview
        - 1.17.4-preview
        - 1.17.5-preview
        - 1.17.6-preview
        - 1.17.13
        - 1.17.15
        - 1.17.17
        - 1.18.2
        - 1.18.4
        - 1.18.9
        - 1.18.11
        - 1.18.13
        - 1.18.15
        - 1.18.16
        - 1.18.19
        - 1.19.4
        - 1.19.6
        - 1.19.9
        - 1.19.11
        - 1.19.13
        - 1.19.14
        - 1.19.15
        - 1.19.17
        - 1.19.18
        - 1.19.19
        - 1.20.0
        - 1.20.3
        - 1.20.5
        - 1.21.1
        - 1.21.2
        - 1.21.3
        - 1.21.8
        - 1.21.9
        - 1.21.10
        - 1.21.12
        - 1.21.14
        - 1.21.15
        - 1.21.17
        - 1.21.18
        - 1.21.19
        - 1.21.20
        - 1.21.21
        - 1.22.2
        - 1.22.3
        - 1.23.1
        - 1.24.0
        - 1.25.0
        - 1.25.1
        - 1.27.0
        - 2.0.3
        - 2.0.4
        - 2.0.6
        - 2.0.8
        - 2.1.0
        - 2.2.2
        - 2.3.0
        - 2.3.1
        - 2.3.7
        - 2.3.16
        - 2.4.1
        - 2.4.2
        - 2.4.3
        - 2.4.4
        - 2.4.5
        - 2.4.6
        - 2.5.0
        - 2.6.0
        - 2.7.0
        - 2.7.2
        compatible:
        - 0.0.8-preview
        - 0.0.12-preview
        - 0.0.15-preview
        - 0.0.16-preview
        - 0.0.18-preview
        - 0.0.22-preview
        - 0.0.26-preview
        - 0.0.27-preview
        - 0.1.2-preview
        - 0.2.1-preview
        - 0.2.2-preview
        - 0.3.5-preview
        - 0.4.6-preview
        - 0.4.8-preview
        - 0.5.2-preview
        - 0.5.3-preview
        - 0.6.6-preview
        - 0.6.7-preview
        - 0.6.8-preview
        - 0.7.4-preview
        - 0.7.5-preview
        - 0.8.4-preview
        - 0.8.6-preview
        - 1.1.3-preview
        - 1.1.4-preview
        - 1.1.5
        - 1.1.7
        - 1.1.9
        - 1.1.10
        - 1.2.2
        - 1.2.3
        - 1.2.4
        - 1.3.3
        - 1.3.8
        - 1.4.0
        - 1.5.0
        - 1.5.1
        - 1.6.0
        - 1.6.2
        - 1.7.4
        - 1.7.5
        - 1.8.3
        - 1.8.4
        - 1.8.5
        - 1.9.2
        - 1.10.0
        - 1.11.2
        - 1.12.0
        - 1.13.1
        - 1.14.2
        - 1.15.1
        - 1.16.1
        - 1.16.6
        - 1.16.7
        - 1.16.8
        - 1.16.10
        - 1.16.12
        - 1.16.13
        - 1.16.15
        - 1.16.16
        - 1.16.19
        - 1.17.0-preview
        - 1.17.2-preview
        - 1.17.4-preview
        - 1.17.5-preview
        - 1.17.6-preview
        - 1.17.13
        - 1.17.15
        - 1.17.17
        - 1.18.2
        - 1.18.4
        - 1.18.9
        - 1.18.11
        - 1.18.13
        - 1.18.15
        - 1.18.16
        - 1.18.19
        - 1.19.4
        - 1.19.6
        - 1.19.9
        - 1.19.11
        - 1.19.13
        - 1.19.14
        - 1.19.15
        - 1.19.17
        - 1.19.18
        - 1.19.19
        - 1.20.0
        - 1.20.3
        - 1.20.5
        - 1.21.1
        - 1.21.2
        - 1.21.3
        - 1.21.8
        - 1.21.9
        - 1.21.10
        - 1.21.12
        - 1.21.14
        - 1.21.15
        - 1.21.17
        - 1.21.18
        - 1.21.19
        - 1.21.20
        - 1.21.21
        - 1.22.2
        - 1.22.3
        - 1.23.1
        - 1.24.0
        - 1.25.0
        - 1.25.1
        verified: 1.18.19
      dependencies:
      - name: com.unity.modules.assetbundle
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.imageconversion
        version: 1.0.0
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.scriptablebuildpipeline
        version: 1.21.3
      - name: com.unity.modules.unitywebrequestassetbundle
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.modules.assetbundle
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.imageconversion
        version: 1.0.0
      - name: com.unity.modules.unitywebrequest
        version: 1.0.0
      - name: com.unity.scriptablebuildpipeline
        version: 1.21.3
      - name: com.unity.modules.unitywebrequestassetbundle
        version: 1.0.0
      keywords:
      - asset
      - resources
      - bundle
      - bundles
      - assetbundles
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638122668020000000
      documentationUrl: https://docs.unity3d.com/Packages/com.unity.addressables@1.21/manual/index.html
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/Addressables.git
        revision: 983b68f6151bf3c52a38fd509d418d1369086e63
        path: 
    - packageId: com.unity.nuget.newtonsoft-json@3.0.2
      testable: 0
      isDirectDependency: 0
      version: 3.0.2
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.nuget.newtonsoft-json@3.0.2
      assetPath: Packages/com.unity.nuget.newtonsoft-json
      name: com.unity.nuget.newtonsoft-json
      displayName: Newtonsoft Json
      author:
        name: 
        email: 
        url: 
      category: 
      type: library
      description: 'Newtonsoft Json for use in Unity projects and Unity packages.
        Currently synced to version 13.0.1.


        This package is used for advanced
        json serialization and deserialization. Most Unity users will be better suited
        using the existing json tools built into Unity.

        To avoid assembly
        clashes, please use this package if you intend to use Newtonsoft Json.'
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0-preview.2
        - 1.0.0-preview.3
        - 1.0.0-preview.4
        - 1.0.1-preview.1
        - 1.1.2
        - 2.0.0-preview
        - 2.0.0-preview.1
        - 2.0.0-preview.2
        - 2.0.0
        - 2.0.1-preview.1
        - 2.0.2
        - 3.0.1
        - 3.0.2
        - 3.1.0
        - 3.2.0
        - 3.2.1
        compatible:
        - 1.0.0-preview.2
        - 1.0.0-preview.3
        - 1.0.0-preview.4
        - 1.0.1-preview.1
        - 1.1.2
        - 2.0.0-preview
        - 2.0.0-preview.1
        - 2.0.0-preview.2
        - 2.0.0
        - 2.0.1-preview.1
        - 2.0.2
        - 3.0.1
        - 3.0.2
        - 3.1.0
        - 3.2.0
        - 3.2.1
        verified: 3.0.2
      dependencies: []
      resolvedDependencies: []
      keywords: []
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 1
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637846828220000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/com.unity.nuget.newtonsoft-json.git
        revision: 8f66870a71ad3f2cdd46330b5c482dfd138ffc64
        path: 
    - packageId: com.unity.2d.path@4.0.2
      testable: 0
      isDirectDependency: 0
      version: 4.0.2
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.2d.path@4.0.2
      assetPath: Packages/com.unity.2d.path
      name: com.unity.2d.path
      displayName: 2D Path
      author:
        name: 
        email: 
        url: 
      category: 2D
      type: asset
      description: "2D Path provides tooling to edit shapes (polygons and B\xE9zier
        splines) in EditorWindows and the SceneView."
      status: 0
      errors: []
      versions:
        all:
        - 1.0.0-preview.1
        - 1.0.0-preview.4
        - 2.0.1
        - 2.0.2
        - 2.0.3
        - 2.0.4
        - 2.0.5
        - 2.0.6
        - 2.1.0
        - 2.1.1
        - 3.0.0
        - 3.0.1
        - 3.0.2
        - 4.0.0
        - 4.0.1
        - 4.0.2
        - 5.0.0-pre.1
        - 5.0.0-pre.2
        - 5.0.0
        - 5.0.1
        - 5.0.2
        compatible:
        - 4.0.2
        verified: 4.0.2
      dependencies: []
      resolvedDependencies: []
      keywords:
      - 2d
      - path
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637637511040000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/2d.git
        revision: 6f9c488e4d487ead34ee4d516f0a2f4a911a8b25
        path: 
    - packageId: com.unity.2d.common@4.2.1
      testable: 0
      isDirectDependency: 0
      version: 4.2.1
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.2d.common@4.2.1
      assetPath: Packages/com.unity.2d.common
      name: com.unity.2d.common
      displayName: 2D Common
      author:
        name: 
        email: 
        url: 
      category: 2D
      type: asset
      description: 2D Common is a package that contains shared functionalities that
        are used by most of the other 2D packages.
      status: 0
      errors: []
      versions:
        all:
        - 1.0.9-preview.1
        - 1.0.9-preview.2
        - 1.0.10-preview
        - 1.0.11-preview.1
        - 1.1.0-preview.1
        - 1.1.0-preview.2
        - 1.2.0-preview.1
        - 2.0.1
        - 2.0.2
        - 2.1.0
        - 2.1.1
        - 2.1.2
        - 3.0.0
        - 3.0.1
        - 4.0.0
        - 4.0.1
        - 4.0.2
        - 4.0.3
        - 4.0.4
        - 4.1.0
        - 4.2.0
        - 4.2.1
        - 5.0.0-pre.1
        - 5.0.0-pre.2
        - 5.0.0
        - 6.0.0-pre.2
        - 6.0.0-pre.3
        - 6.0.0-pre.4
        - 6.0.0
        - 6.0.1
        - 6.0.2
        - 6.0.3
        - 6.0.4
        - 6.0.5
        - 6.0.6
        - 6.0.7
        - 6.0.8
        - 6.1.0
        - 7.0.0-pre.3
        - 7.0.0-pre.4
        - 7.0.0
        - 7.0.1
        - 7.0.2
        - 7.0.3
        - 8.0.0-pre.1
        - 8.0.0-pre.2
        - 8.0.0
        - 8.0.1
        - 8.0.2
        - 8.0.3
        - 8.0.4
        - 8.1.0
        - 8.1.1
        - 9.0.0-pre.1
        - 9.0.0-pre.2
        - 9.0.0
        - 9.0.1
        - 9.0.2
        - 9.0.3
        - 9.0.4
        - 9.0.5
        - 9.0.6
        - 9.0.7
        - 9.1.0
        - 9.1.1
        - 10.0.0
        - 11.0.0
        - 11.0.1
        - 12.0.0
        compatible:
        - 4.2.0
        - 4.2.1
        verified: 4.2.1
      dependencies:
      - name: com.unity.2d.sprite
        version: 1.0.0
      - name: com.unity.modules.uielements
        version: 1.0.0
      resolvedDependencies:
      - name: com.unity.2d.sprite
        version: 1.0.0
      - name: com.unity.modules.uielements
        version: 1.0.0
      - name: com.unity.modules.ui
        version: 1.0.0
      - name: com.unity.modules.imgui
        version: 1.0.0
      - name: com.unity.modules.jsonserialize
        version: 1.0.0
      - name: com.unity.modules.uielementsnative
        version: 1.0.0
      keywords:
      - 2d
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 637988300970000000
      documentationUrl: 
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/2d.git
        revision: c016f0f10972606d85ccfbf9de2033ad5384d6c4
        path: 
    - packageId: com.unity.scriptablebuildpipeline@1.21.3
      testable: 0
      isDirectDependency: 0
      version: 1.21.3
      source: 1
      resolvedPath: H:\Works\TS\Library\PackageCache\com.unity.scriptablebuildpipeline@1.21.3
      assetPath: Packages/com.unity.scriptablebuildpipeline
      name: com.unity.scriptablebuildpipeline
      displayName: Scriptable Build Pipeline
      author:
        name: 
        email: 
        url: 
      category: 
      type: asset
      description: The Scriptable Build Pipeline moves the asset bundle build pipeline
        to C#.  Use the pre-defined build flows, or create your own using the divided
        up APIs.  This system improves build time, fixes incremental build, and provides
        greater flexibility.
      status: 0
      errors: []
      versions:
        all:
        - 0.0.5-preview
        - 0.0.6-preview
        - 0.0.8-preview
        - 0.0.9-preview
        - 0.0.10-preview
        - 0.0.14-preview
        - 0.0.15-preview
        - 0.1.0-preview
        - 0.2.0-preview
        - 1.0.1-preview
        - 1.1.0-preview
        - 1.1.1-preview
        - 1.2.1-preview
        - 1.3.5-preview
        - 1.4.1-preview
        - 1.5.0-preview
        - 1.5.1
        - 1.5.2
        - 1.5.4
        - 1.5.6
        - 1.5.10
        - 1.6.3-preview
        - 1.6.4-preview
        - 1.6.5-preview
        - 1.7.2
        - 1.7.3
        - 1.8.2
        - 1.8.4
        - 1.8.6
        - 1.9.0
        - 1.10.0
        - 1.11.1
        - 1.11.2
        - 1.12.0
        - 1.13.1
        - 1.14.0
        - 1.14.1
        - 1.15.1
        - 1.15.2
        - 1.16.1
        - 1.17.0
        - 1.18.0
        - 1.19.0
        - 1.19.1
        - 1.19.2
        - 1.19.3
        - 1.19.4
        - 1.19.5
        - 1.19.6
        - 1.20.1
        - 1.20.2
        - 1.21.0
        - 1.21.1
        - 1.21.2
        - 1.21.3
        - 1.21.5
        - 1.21.7
        - 1.21.8
        - 1.21.9
        - 1.21.20
        - 1.21.21
        - 1.21.22
        - 1.21.23
        - 1.21.24
        - 1.21.25
        - 1.22.1
        - 1.22.2
        - 1.22.4
        - 1.22.5
        - 1.23.0
        - 2.0.1
        - 2.0.2
        - 2.1.0
        - 2.1.2
        - 2.1.3
        - 2.1.4
        - 2.1.5
        - 2.2.4
        - 2.2.11
        - 2.3.0
        - 2.3.1
        - 2.3.2
        - 2.3.3
        - 2.3.4
        - 2.3.5
        - 2.3.6
        - 2.3.7
        - 2.3.8
        - 2.4.0
        - 2.4.1
        compatible:
        - 0.0.5-preview
        - 0.0.6-preview
        - 0.0.8-preview
        - 0.0.9-preview
        - 0.0.10-preview
        - 0.0.14-preview
        - 0.0.15-preview
        - 0.1.0-preview
        - 0.2.0-preview
        - 1.0.1-preview
        - 1.1.0-preview
        - 1.1.1-preview
        - 1.2.1-preview
        - 1.3.5-preview
        - 1.4.1-preview
        - 1.5.0-preview
        - 1.5.1
        - 1.5.2
        - 1.5.4
        - 1.5.6
        - 1.5.10
        - 1.6.3-preview
        - 1.6.4-preview
        - 1.6.5-preview
        - 1.7.2
        - 1.7.3
        - 1.8.2
        - 1.8.4
        - 1.8.6
        - 1.9.0
        - 1.10.0
        - 1.11.1
        - 1.11.2
        - 1.12.0
        - 1.13.1
        - 1.14.0
        - 1.14.1
        - 1.15.1
        - 1.15.2
        - 1.16.1
        - 1.17.0
        - 1.18.0
        - 1.19.0
        - 1.19.1
        - 1.19.2
        - 1.19.3
        - 1.19.4
        - 1.19.5
        - 1.19.6
        - 1.20.1
        - 1.20.2
        - 1.21.0
        - 1.21.1
        - 1.21.2
        - 1.21.3
        - 1.21.5
        - 1.21.7
        - 1.21.8
        - 1.21.9
        - 1.21.20
        - 1.21.21
        - 1.21.22
        - 1.21.23
        - 1.21.24
        - 1.21.25
        - 1.22.1
        - 1.22.2
        - 1.22.4
        - 1.22.5
        - 2.0.1
        - 2.0.2
        - 2.1.0
        - 2.1.2
        - 2.1.3
        - 2.1.4
        - 2.1.5
        - 2.2.4
        verified: 1.20.2
      dependencies: []
      resolvedDependencies: []
      keywords:
      - build
      - bundle
      - bundles
      - assetbundles
      - cache
      - server
      registry:
        id: main
        name: 
        url: https://packages.unity.com
        scopes: []
        isDefault: 1
        capabilities: 7
        configSource: 0
      hasRegistry: 1
      hideInEditor: 0
      entitlements:
        isAllowed: 1
      isAssetStorePackage: 0
      datePublishedTicks: 638122668000000000
      documentationUrl: https://docs.unity3d.com/Packages/com.unity.scriptablebuildpipeline@1.21/manual/index.html
      hasRepository: 1
      repository:
        type: git
        url: https://github.cds.internal.unity3d.com/unity/Addressables.git
        revision: 983b68f6151bf3c52a38fd509d418d1369086e63
        path: 
  m_LocalPackages:
    m_LocalFileStatus:
    - m_FilePath: H:\Works\TS\LocalPackages\com.unity.render-pipelines.high-definition-config/package.json
      m_PathExists: 1
      m_ContentTrackingEnabled: 1
      m_ModificationDate:
        serializedVersion: 2
        ticks: 638036703840000000
      m_Hash: 968112398
    - m_FilePath: H:\Works\TS\LocalPackages\com.unity.render-pipelines.high-definition-config
      m_PathExists: 1
      m_ContentTrackingEnabled: 1
      m_ModificationDate:
        serializedVersion: 2
        ticks: 0
      m_Hash: 0
m_ProjectPath: H:/Works/TS/Packages
m_EditorVersion: 2020.3.42f1 (7ade1201f527)
