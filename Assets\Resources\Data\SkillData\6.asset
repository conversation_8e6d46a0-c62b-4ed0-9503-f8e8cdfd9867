%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec6a3d0267be4c249a394b8e806ee823, type: 3}
  m_Name: 6
  m_EditorClassIdentifier: 
  skillId: 6
  skillName: Skill - ConvertStacksToVenom
  description: "\u5F53\u654C\u4EBA\u8EAB\u4E0A\u7684\u4E2D\u6BD2\u72B6\u6001\u5C42\u6570\u4E3A10\u5C42\u65F6\uFF0C\u6D88\u9664\u8BE5\u72B6\u6001\u5E76\u4F7F\u654C\u4EBA\u83B7\u5F97\u4E00\u5C42\u731B\u6BD2"
  skillCd: 0
  cdRemain: 0
  skillDeployer: 0
  skillReleaseChecker: CheckBuffStackLayer
  skillReleaseCheckerJson: "\"{\u201DrequiredBuffTypeID\u201D:\u201D1008\u201D&\"\"LayerNum\"\":\"\"10\"\"}\""
  attenuationType: 0
  coefficientType: 1
  attackCoeffient: 0.5
  CoeffientBaseTags: []
  scriptName: 1
  propertyField: _validMaxHealth
  directionType: 0
  coefficient: 0
  buffTypeId: d1070000
  attachedProperty: []
  attackDistance: 1
  attackAngle: 360
  attackTargetTags:
  - Enemy
  attackTargets: []
  impactType: 2
  durationTime: 0
  attackInterval: 0
  owner: {fileID: 0}
  prefabName: 
  skillPrefab: {fileID: 0}
  animationName: 
  hitFxName: 
  hitFxPrefab: {fileID: 0}
  level: 0
  attackType: 0
  scatterType: 0
  selectorType: 3
  skillIndicator: 
  skillIconName: 
  skillIcon: {fileID: 0}
  disappearType: 0
