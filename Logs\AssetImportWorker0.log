Using pre-set license
Built from '2020.3/staging' branch; Version is '2020.3.42f1 (7ade1201f527) revision 8052242'; Using compiler version '192528614'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'zh' Physical Memory: 32607 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0

COMMAND LINE ARGUMENTS:
H:\Works\2020.3.42f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
H:/Works/TS
-logFile
Logs/AssetImportWorker0.log
-srvPort
56912
Successfully changed project path to: H:/Works/TS
H:/Works/TS
Using Asset Import Pipeline V2.
Refreshing native plugins compatible for Editor in 106.68 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2020.3.42f1 (7ade1201f527)
[Subsystems] Discovering subsystems at path H:/Works/2020.3.42f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path H:/Works/TS/Assets
GfxDevice: creating device client; threaded=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 SUPER (ID=0x2783)
    Vendor:   
    VRAM:     11999 MB
    Driver:   32.0.15.7283
Initialize mono
Mono path[0] = 'H:/Works/2020.3.42f1/Editor/Data/Managed'
Mono path[1] = 'H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit'
Mono config path = 'H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56580
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: H:/Works/2020.3.42f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.000757 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 96.18 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.490 seconds
Domain Reload Profiling:
	ReloadAssembly (490ms)
		BeginReloadAssembly (56ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (385ms)
			LoadAssemblies (56ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (94ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (17ms)
			SetupLoadedEditorAssemblies (199ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (96ms)
				BeforeProcessingInitializeOnLoad (12ms)
				ProcessInitializeOnLoadAttributes (68ms)
				ProcessInitializeOnLoadMethodAttributes (20ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.007028 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 105.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.365 seconds
Domain Reload Profiling:
	ReloadAssembly (1366ms)
		BeginReloadAssembly (102ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (13ms)
		EndReloadAssembly (1229ms)
			LoadAssemblies (104ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (300ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (66ms)
			SetupLoadedEditorAssemblies (700ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (105ms)
				BeforeProcessingInitializeOnLoad (94ms)
				ProcessInitializeOnLoadAttributes (481ms)
				ProcessInitializeOnLoadMethodAttributes (15ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (1ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.11 seconds
Refreshing native plugins compatible for Editor in 1.06 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6468 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 288.5 MB.
System memory in use after: 288.9 MB.

Unloading 45 unused Assets to reduce memory usage. Loaded Objects now: 6926.
Total: 3.861900 ms (FindLiveObjects: 0.299900 ms CreateObjectMapping: 0.156200 ms MarkObjects: 3.374100 ms  DeleteObjects: 0.030700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  path: Assets/Resources/Data/Player/Alice.asset
  artifactKey: Guid(40ce26f1d82f30d4c9c7abde19141087) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/Player/Alice.asset using Guid(40ce26f1d82f30d4c9c7abde19141087) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8b666f64869626d3cd9656d6a9b63451') in 0.032499 seconds 
========================================================================
Received Import Request.
  Time since last request: 66.682229 seconds.
  path: Assets/Resources/Prefabs/Tower/ChainLightningTower.prefab
  artifactKey: Guid(4b5d9eaa2a0498b44a5328aa1d35f8a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Tower/ChainLightningTower.prefab using Guid(4b5d9eaa2a0498b44a5328aa1d35f8a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3360b0edd518bc4ce7edad64647bc2a5') in 0.097197 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000131 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect12.png
  artifactKey: Guid(09fc34b47dcbc3b43ac8880fcac6478e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect12.png using Guid(09fc34b47dcbc3b43ac8880fcac6478e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '94c27eedda0c619eaa26dd830f2907d4') in 0.045155 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000122 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect25.png
  artifactKey: Guid(7ba0bce2c6fdbae4f97454c5aa7c53f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect25.png using Guid(7ba0bce2c6fdbae4f97454c5aa7c53f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '73ff75e042797ab11dbeef5af88891cd') in 0.011032 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000274 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect22.png
  artifactKey: Guid(c10d73e731d15294ea3488a2a3c2ce51) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect22.png using Guid(c10d73e731d15294ea3488a2a3c2ce51) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '529897da518e45a9da2808ec789161d2') in 0.009246 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000166 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect42.png
  artifactKey: Guid(a461b4239d9b169458713bedbc864d2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect42.png using Guid(a461b4239d9b169458713bedbc864d2d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f8f4ec5d53db869d2593cebcf736e0c5') in 0.009919 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000142 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect51.png
  artifactKey: Guid(618e56d46f735a7418ce357805143564) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect51.png using Guid(618e56d46f735a7418ce357805143564) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c56445d5c1384944a737e2e3b67c8168') in 0.009950 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000225 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect20.png
  artifactKey: Guid(46ceb7a9405f2434389bf775568a83bd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect20.png using Guid(46ceb7a9405f2434389bf775568a83bd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5aefcc5b0ab957502d4d3d63310292e9') in 0.028698 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000203 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect45.png
  artifactKey: Guid(913af7656bcd7544c9d36a4781f80f02) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect45.png using Guid(913af7656bcd7544c9d36a4781f80f02) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '71c055915a98e3ae4f42ffadf6165110') in 0.033529 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000212 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect53.png
  artifactKey: Guid(c1fcf3d8f2578a1419797c5dc6c87a44) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect53.png using Guid(c1fcf3d8f2578a1419797c5dc6c87a44) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd19015acb8744df373d78ecf86830f54') in 0.024227 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000244 seconds.
  path: Assets/Art/Sprites/Tower/lighttower.png
  artifactKey: Guid(246081a5bac5ef44db21379b11a1a94b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower.png using Guid(246081a5bac5ef44db21379b11a1a94b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5a822f6af499302ca233b1ed8573d347') in 0.045013 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000159 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect40.png
  artifactKey: Guid(bdcb2f7412595e74ca5f0c9c10cfd584) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect40.png using Guid(bdcb2f7412595e74ca5f0c9c10cfd584) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8f7acd63d6af3cebe7210525b29582d6') in 0.011522 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000248 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect47.png
  artifactKey: Guid(ad7ce30541d13b34c9cf85de124c395d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect47.png using Guid(ad7ce30541d13b34c9cf85de124c395d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '042a66b02e1aeac10a2871e97a10bce1') in 0.012224 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000209 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect52.png
  artifactKey: Guid(44d55c65bcbe14e4590097faf651bb1e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect52.png using Guid(44d55c65bcbe14e4590097faf651bb1e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b0e3767f25d697d0333e582c11a9628d') in 0.021099 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000215 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect21.png
  artifactKey: Guid(c8622eb966b5b7c4d95246fac8808fee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect21.png using Guid(c8622eb966b5b7c4d95246fac8808fee) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3fabdc0cabb98ea9c6fbde687c1fbd25') in 0.041782 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000160 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect17.png
  artifactKey: Guid(7a87b8a604867934396f33ade10118f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect17.png using Guid(7a87b8a604867934396f33ade10118f0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6d4b7c54c713ee39393b6f4c6538f4b3') in 0.010024 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000185 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect02.png
  artifactKey: Guid(94322818b882d42418475fa438cf2043) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect02.png using Guid(94322818b882d42418475fa438cf2043) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e61bfe3f4801484bed05a25a1898ae40') in 0.009863 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000170 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect03.png
  artifactKey: Guid(658482acaea64ef4aa4ebcca9fab0e3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect03.png using Guid(658482acaea64ef4aa4ebcca9fab0e3e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e6b2998f22d0357bb26ac5fbc9bc3bbf') in 0.010459 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000213 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect14.png
  artifactKey: Guid(3ad25c9be83e05440aae22371044fead) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect14.png using Guid(3ad25c9be83e05440aae22371044fead) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8e22721fbeb9e12072d1edc5ca47f01a') in 0.009664 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000183 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect04.png
  artifactKey: Guid(8b91e946a50c6264e9c2fcf7078fea3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect04.png using Guid(8b91e946a50c6264e9c2fcf7078fea3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '97ff44b1c8046df49970aff4685880d7') in 0.009494 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000229 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect11.png
  artifactKey: Guid(1561d054a16ebfb43937831b6dce4b5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect11.png using Guid(1561d054a16ebfb43937831b6dce4b5f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6d78ff8a101c05b71327680705c6b071') in 0.009399 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000157 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect01.png
  artifactKey: Guid(ffe300cb1e6e01a439f4b601d92c5e3d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect01.png using Guid(ffe300cb1e6e01a439f4b601d92c5e3d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4817cb7281a5a7824bc11db5f0490cef') in 0.009366 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000209 seconds.
  path: Assets/Resources/Prefabs/Summor/LightingBird.prefab
  artifactKey: Guid(7e661eea0b3b4954897b0f8e4f2dfb6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Summor/LightingBird.prefab using Guid(7e661eea0b3b4954897b0f8e4f2dfb6d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) Tried select unknown importer for id '-2' '00000000000000000000000000000000'
0x00007ff6e71ddefc (Unity) StackWalker::GetCurrentCallstack
0x00007ff6e71e54f9 (Unity) StackWalker::ShowCallstack
0x00007ff6e7f7f28c (Unity) GetStacktrace
0x00007ff6e89eedd7 (Unity) DebugStringToFile
0x00007ff6e89eeaf2 (Unity) AssertImplementation
0x00007ff6e8002ad1 (Unity) AssetDatabase::LookupAssetImporter
0x00007ff6e8008f28 (Unity) PreviewImporter::GenerateAssetData
0x00007ff6e80ac8d3 (Unity) ImportToObjects
0x00007ff6e80ab8a8 (Unity) ImportAsset
0x00007ff6e80446b8 (Unity) AssetImportWorker::Import
0x00007ff6e8069e8c (Unity) <lambda_6c894734dbee50c93f7912c8ad287316>::operator()
0x00007ff6e80f9bf2 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff6e80e5a92 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff6e80e79a4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff6e80f7f2f (Unity) IOService::Run
0x00007ff6e8093bfb (Unity) RunAssetImportWorkerClientV2
0x00007ff6e8093c50 (Unity) RunAssetImporterV2
0x00007ff6e7b06bd8 (Unity) Application::InitializeProject
0x00007ff6e7f89612 (Unity) WinMain
0x00007ff6e965765e (Unity) __scrt_common_main_seh
0x00007fffcec8259d (KERNEL32) BaseThreadInitThunk
0x00007fffcf08af78 (ntdll) RtlUserThreadStart

 -> (artifact id: 'a80808dd1fd9b8741d2c5ece9b464949') in 0.173863 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.009876 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect44.png
  artifactKey: Guid(ad3f9584992bb904cbff0199548044e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect44.png using Guid(ad3f9584992bb904cbff0199548044e8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c0a8dbdefa1797b4aef640fef9243591') in 0.009419 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000208 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect41.png
  artifactKey: Guid(99e0ee1a599999a47810ae8ba7f9f1bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect41.png using Guid(99e0ee1a599999a47810ae8ba7f9f1bf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '66311cc6c310c8d46f3eb100940f98c5') in 0.009588 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000212 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect43.png
  artifactKey: Guid(3474bc600e3da5845959feb29f510fe0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect43.png using Guid(3474bc600e3da5845959feb29f510fe0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '06d2e2d878ee1facbeef6e0104d52084') in 0.010056 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000215 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect27.png
  artifactKey: Guid(87bb6dbdedefbe64991eec5943a43a69) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect27.png using Guid(87bb6dbdedefbe64991eec5943a43a69) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ab026a5944df6881aa8392da4717137c') in 0.010010 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000173 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect19.png
  artifactKey: Guid(7c048d7921c4f814cb41a393571a769a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect19.png using Guid(7c048d7921c4f814cb41a393571a769a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '38e6ef7138c33f19d089d88aaf7acf7c') in 0.009520 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000396 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect18.png
  artifactKey: Guid(8a7b9e841729e9343acec4b24b8280db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect18.png using Guid(8a7b9e841729e9343acec4b24b8280db) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2c30fd3418e9efd29a824e72e0b95222') in 0.008960 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000190 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect00.png
  artifactKey: Guid(b3b06c9cd95027a4b90dc6849075c8e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect00.png using Guid(b3b06c9cd95027a4b90dc6849075c8e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1b155811e0f5d17a1dde4281e04e1983') in 0.009615 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000173 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect23.png
  artifactKey: Guid(5290c43fd924134489756a3faebf5d94) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect23.png using Guid(5290c43fd924134489756a3faebf5d94) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '9d4f1b6065036ae55a1745e6b00227c6') in 0.010087 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000193 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect15.png
  artifactKey: Guid(92c33347ac3791247b07c413938b0115) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect15.png using Guid(92c33347ac3791247b07c413938b0115) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f60602f03be9b114428aaefad2a9eb43') in 0.010363 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000331 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect50.png
  artifactKey: Guid(0375eb8d126fc764d87846029e576d56) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect50.png using Guid(0375eb8d126fc764d87846029e576d56) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '376b4addd30257a6f822190006246602') in 0.009925 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000222 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect38.png
  artifactKey: Guid(14c3629f062cafc4dac653f18e63dcf2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect38.png using Guid(14c3629f062cafc4dac653f18e63dcf2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0bce1c94286b897536e78b3cec8522f7') in 0.010562 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000222 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect08.png
  artifactKey: Guid(fe4643e8a34691740abe161be75554bd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect08.png using Guid(fe4643e8a34691740abe161be75554bd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1ccb0b61c4030b9372b8133d6530e633') in 0.010066 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000172 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect05.png
  artifactKey: Guid(6edaa3c3833bbc2459c08b97e0925199) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect05.png using Guid(6edaa3c3833bbc2459c08b97e0925199) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fffd245bbb9fa1e2a605b8d97d703c08') in 0.009478 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000182 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect28.png
  artifactKey: Guid(8775b49e847a2ec4a8ed5a5db0965ff3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect28.png using Guid(8775b49e847a2ec4a8ed5a5db0965ff3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '11759ebe21c1d9e7759dea1a2fe9a7b3') in 0.009665 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000177 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect07.png
  artifactKey: Guid(09404a8191fe84c4c9a1096f88e4926b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect07.png using Guid(09404a8191fe84c4c9a1096f88e4926b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd0429c9bda2d84bde5aee61e0cd781d0') in 0.010422 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000218 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect49.png
  artifactKey: Guid(1867c6736496d684693f13dde6083300) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect49.png using Guid(1867c6736496d684693f13dde6083300) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4dfcce9c84edf22452f51d84092bece3') in 0.009851 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000171 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect39.png
  artifactKey: Guid(aba3fbc2d726bb04391b27f7f9516335) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect39.png using Guid(aba3fbc2d726bb04391b27f7f9516335) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7c5fa0e28019bc1c1a124a8a38741877') in 0.009944 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000166 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect48.png
  artifactKey: Guid(070af8a752f7635429dc5a79340b22ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect48.png using Guid(070af8a752f7635429dc5a79340b22ff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '737760f73c98bfb6fbcc69bf0bd23328') in 0.009383 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000179 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect10.png
  artifactKey: Guid(1ded66256e3bc254aaf12fd1a7acc00d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect10.png using Guid(1ded66256e3bc254aaf12fd1a7acc00d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2a2bce2e6988227a599b84d2a82de673') in 0.009655 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000137 seconds.
  path: Assets/Art/Sprites/Tower/lightningtower.png
  artifactKey: Guid(6bd3f607bc2e17c4b9dcd0514fceccb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lightningtower.png using Guid(6bd3f607bc2e17c4b9dcd0514fceccb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a6db5b648ca4f41e73487a101363c1be') in 0.010310 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000223 seconds.
  path: Assets/Resources/Prefabs/Tower/LightTower.prefab
  artifactKey: Guid(19724c7af8983c9428af767560f0306d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Tower/LightTower.prefab using Guid(19724c7af8983c9428af767560f0306d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a5cefb454585ffb7cc68e42658fd37ac') in 0.081669 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000162 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect09.png
  artifactKey: Guid(2d91f9d8c11751a4287ff39c634274f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect09.png using Guid(2d91f9d8c11751a4287ff39c634274f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '957bed795eaf7f1406d78a9c7da9f12e') in 0.009664 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000155 seconds.
  path: Assets/lightning_orb_effect_transparent.gif
  artifactKey: Guid(55db5b600e755254fb8d49a58f6d661a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/lightning_orb_effect_transparent.gif using Guid(55db5b600e755254fb8d49a58f6d661a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '49bec8abf72eb7f98ee2a0b55ac8754d') in 0.012276 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000163 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect13.png
  artifactKey: Guid(98845818b610fbc4da2a7724f854cd51) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect13.png using Guid(98845818b610fbc4da2a7724f854cd51) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a43675050e90af5119bb99bb8670e038') in 0.008688 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000187 seconds.
  path: Assets/Art/Sprites/Tower/lightningtower_base.png
  artifactKey: Guid(3478c20ed471db34ca4c5b69311caeec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lightningtower_base.png using Guid(3478c20ed471db34ca4c5b69311caeec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8b4d401a32b013b3ba0e11407e32c976') in 0.030575 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.006742 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.194 seconds
Domain Reload Profiling:
	ReloadAssembly (1195ms)
		BeginReloadAssembly (108ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (38ms)
		EndReloadAssembly (1052ms)
			LoadAssemblies (106ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (305ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (56ms)
			SetupLoadedEditorAssemblies (499ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (84ms)
				ProcessInitializeOnLoadAttributes (398ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 273.6 MB.
System memory in use after: 274.1 MB.

Unloading 35 unused Assets to reduce memory usage. Loaded Objects now: 6943.
Total: 4.171100 ms (FindLiveObjects: 0.460900 ms CreateObjectMapping: 0.187600 ms MarkObjects: 3.429400 ms  DeleteObjects: 0.092000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.006618 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.158 seconds
Domain Reload Profiling:
	ReloadAssembly (1158ms)
		BeginReloadAssembly (101ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (35ms)
		EndReloadAssembly (1021ms)
			LoadAssemblies (98ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (287ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (49ms)
			SetupLoadedEditorAssemblies (511ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (82ms)
				ProcessInitializeOnLoadAttributes (413ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.01 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 273.7 MB.
System memory in use after: 274.2 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6946.
Total: 3.520100 ms (FindLiveObjects: 0.319400 ms CreateObjectMapping: 0.179700 ms MarkObjects: 2.991300 ms  DeleteObjects: 0.028800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 120.721126 seconds.
  path: Assets/Resources/Data/SkillData/9.asset
  artifactKey: Guid(c41116d432c1c1145a9cdcf2e0861d61) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/9.asset using Guid(c41116d432c1c1145a9cdcf2e0861d61) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '77d9219fc2e51a55e96f27aeb0c4cf1a') in 0.016410 seconds 
