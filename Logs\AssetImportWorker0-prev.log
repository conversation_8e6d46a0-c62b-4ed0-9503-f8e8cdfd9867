Using pre-set license
Built from '2020.3/staging' branch; Version is '2020.3.42f1 (7ade1201f527) revision 8052242'; Using compiler version '192528614'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit Professional' Language: 'zh' Physical Memory: 32607 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 0

COMMAND LINE ARGUMENTS:
H:\Works\2020.3.42f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
H:/Works/TS
-logFile
Logs/AssetImportWorker0.log
-srvPort
64126
Successfully changed project path to: H:/Works/TS
H:/Works/TS
Using Asset Import Pipeline V2.
Refreshing native plugins compatible for Editor in 101.59 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2020.3.42f1 (7ade1201f527)
[Subsystems] Discovering subsystems at path H:/Works/2020.3.42f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path H:/Works/TS/Assets
GfxDevice: creating device client; threaded=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 SUPER (ID=0x2783)
    Vendor:   
    VRAM:     11999 MB
    Driver:   32.0.15.7283
Initialize mono
Mono path[0] = 'H:/Works/2020.3.42f1/Editor/Data/Managed'
Mono path[1] = 'H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit'
Mono config path = 'H:/Works/2020.3.42f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56292
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: H:/Works/2020.3.42f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.000813 seconds.
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 104.03 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  0.456 seconds
Domain Reload Profiling:
	ReloadAssembly (456ms)
		BeginReloadAssembly (53ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (0ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (1ms)
		EndReloadAssembly (359ms)
			LoadAssemblies (53ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (86ms)
			ReleaseScriptCaches (0ms)
			RebuildScriptCaches (18ms)
			SetupLoadedEditorAssemblies (188ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (104ms)
				BeforeProcessingInitializeOnLoad (9ms)
				ProcessInitializeOnLoadAttributes (52ms)
				ProcessInitializeOnLoadMethodAttributes (19ms)
				AfterProcessingInitializeOnLoad (0ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (0ms)
Platform modules already initialized, skipping
Registering precompiled user dll's ...
Registered in 0.006094 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 104.34 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.399 seconds
Domain Reload Profiling:
	ReloadAssembly (1399ms)
		BeginReloadAssembly (103ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (3ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (13ms)
		EndReloadAssembly (1260ms)
			LoadAssemblies (111ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (337ms)
			ReleaseScriptCaches (1ms)
			RebuildScriptCaches (65ms)
			SetupLoadedEditorAssemblies (687ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (1ms)
				RefreshPlugins (104ms)
				BeforeProcessingInitializeOnLoad (93ms)
				ProcessInitializeOnLoadAttributes (470ms)
				ProcessInitializeOnLoadMethodAttributes (16ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (1ms)
Platform modules already initialized, skipping
========================================================================
Worker process is ready to serve import requests
Launched and connected shader compiler UnityShaderCompiler.exe after 0.06 seconds
Refreshing native plugins compatible for Editor in 1.46 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6468 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 288.5 MB.
System memory in use after: 289.0 MB.

Unloading 45 unused Assets to reduce memory usage. Loaded Objects now: 6926.
Total: 4.413600 ms (FindLiveObjects: 0.307100 ms CreateObjectMapping: 0.161500 ms MarkObjects: 3.902800 ms  DeleteObjects: 0.040800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  path: Assets/Resources_moved/Prefabs/Enemy/StandardEnemy.prefab
  artifactKey: Guid(21e48127f068c9940846dbf0b2c33fd2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources_moved/Prefabs/Enemy/StandardEnemy.prefab using Guid(21e48127f068c9940846dbf0b2c33fd2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) Asset 'Familiar': Transition 'AnyState -> slime_idle' in state 'AnyState' doesn't have an Exit Time or any condition, transition will be ignored
0x00007ff708dcdefc (Unity) StackWalker::GetCurrentCallstack
0x00007ff708dd54f9 (Unity) StackWalker::ShowCallstack
0x00007ff709b6f28c (Unity) GetStacktrace
0x00007ff70a5dedd7 (Unity) DebugStringToFile
0x00007ff7096aaa90 (Unity) AnimatorStateMachine::BuildTransitionConstant
0x00007ff7096a9fea (Unity) AnimatorStateMachine::BuildRuntimeAsset
0x00007ff70a414a83 (Unity) AnimatorController::BuildAsset
0x00007ff70a418d6f (Unity) AnimatorController::GetAnimationSetBindings
0x00007ff7080a87fa (Unity) AnimatorControllerPlayable::GenerateGraph
0x00007ff7080ae5d5 (Unity) AnimatorControllerPlayable::SetAnimatorController
0x00007ff7080458de (Unity) Animator::CreateInternalControllerPlayable
0x00007ff7080421c8 (Unity) Animator::AddToManager
0x00007ff7080427a4 (Unity) Animator::AwakeFromLoad
0x00007ff708da6397 (Unity) AwakeFromLoadQueue::InvokeAwakeFromLoad
0x00007ff708da0ec3 (Unity) AwakeFromLoadQueue::AwakeFromLoadAllQueues
0x00007ff70883be3e (Unity) AwakeAndActivateClonedObjects
0x00007ff7096cecd5 (Unity) InstantiateObjectRemoveAllNonAnimationComponents
0x00007ff70a12433b (Unity) EditorUtility_CUSTOM_Internal_InstantiateRemoveAllNonAnimationComponentsSingle_Injected
0x000001bd812c3053 (Mono JIT Code) (wrapper managed-to-native) UnityEditor.EditorUtility:Internal_InstantiateRemoveAllNonAnimationComponentsSingle_Injected (UnityEngine.Object,UnityEngine.Vector3&,UnityEngine.Quaternion&)
0x000001bd812c2f83 (Mono JIT Code) UnityEditor.EditorUtility:Internal_InstantiateRemoveAllNonAnimationComponentsSingle (UnityEngine.Object,UnityEngine.Vector3,UnityEngine.Quaternion)
0x000001bd812c2eb3 (Mono JIT Code) UnityEditor.EditorUtility:InstantiateRemoveAllNonAnimationComponents (UnityEngine.Object,UnityEngine.Vector3,UnityEngine.Quaternion)
0x000001bd812c29b3 (Mono JIT Code) UnityEditor.EditorUtility:InstantiateForAnimatorPreview (UnityEngine.Object)
0x000001bd812c27a3 (Mono JIT Code) UnityEditor.GameObjectInspector/PreviewData:UpdateGameObject (UnityEngine.Object)
0x000001bd8127ec33 (Mono JIT Code) UnityEditor.GameObjectInspector/PreviewData:.ctor (UnityEngine.Object)
0x000001bd8127e7a3 (Mono JIT Code) UnityEditor.GameObjectInspector:GetPreviewData ()
0x000001bd8127e2e3 (Mono JIT Code) UnityEditor.GameObjectInspector:RenderStaticPreview (string,UnityEngine.Object[],int,int)
0x000001bd8127508a (Mono JIT Code) UnityEditor.AssetPreviewUpdater:CreatePreviewForAsset (UnityEngine.Object,UnityEngine.Object[],string)
0x000001bd812751b8 (Mono JIT Code) (wrapper runtime-invoke) <Module>:runtime_invoke_object_object_object_object (object,intptr,intptr,intptr)
0x00007fff09cd00e0 (mono-2.0-bdwgc) [mini-runtime.c:2849] mono_jit_runtime_invoke 
0x00007fff09c52ac2 (mono-2.0-bdwgc) [object.c:2921] do_runtime_invoke 
0x00007fff09c5bb1f (mono-2.0-bdwgc) [object.c:2968] mono_runtime_invoke 
0x00007ff708d12184 (Unity) scripting_method_invoke
0x00007ff708d0d281 (Unity) ScriptingInvocation::Invoke
0x00007ff709bfabc6 (Unity) MonoCreateAssetPreview
0x00007ff709bfca4e (Unity) WriteAssetPreviewToFile
0x00007ff709bfd1b2 (Unity) WritePreviews
0x00007ff709bf8d41 (Unity) PreviewImporter::GenerateAssetData
0x00007ff709c9c8d3 (Unity) ImportToObjects
0x00007ff709c9b8a8 (Unity) ImportAsset
0x00007ff709c346b8 (Unity) AssetImportWorker::Import
0x00007ff709c59e8c (Unity) <lambda_6c894734dbee50c93f7912c8ad287316>::operator()
0x00007ff709ce9bf2 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff709cd5a92 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff709cd79a4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff709ce7f2f (Unity) IOService::Run
0x00007ff709c83bfb (Unity) RunAssetImportWorkerClientV2
0x00007ff709c83c50 (Unity) RunAssetImporterV2
0x00007ff7096f6bd8 (Unity) Application::InitializeProject
0x00007ff709b79612 (Unity) WinMain
0x00007ff70b24765e (Unity) __scrt_common_main_seh
0x00007fffcec8259d (KERNEL32) BaseThreadInitThunk
0x00007fffcf08af78 (ntdll) RtlUserThreadStart

Tried select unknown importer for id '-2' '00000000000000000000000000000000'
0x00007ff708dcdefc (Unity) StackWalker::GetCurrentCallstack
0x00007ff708dd54f9 (Unity) StackWalker::ShowCallstack
0x00007ff709b6f28c (Unity) GetStacktrace
0x00007ff70a5dedd7 (Unity) DebugStringToFile
0x00007ff70a5deaf2 (Unity) AssertImplementation
0x00007ff709bf2ad1 (Unity) AssetDatabase::LookupAssetImporter
0x00007ff709bf8f28 (Unity) PreviewImporter::GenerateAssetData
0x00007ff709c9c8d3 (Unity) ImportToObjects
0x00007ff709c9b8a8 (Unity) ImportAsset
0x00007ff709c346b8 (Unity) AssetImportWorker::Import
0x00007ff709c59e8c (Unity) <lambda_6c894734dbee50c93f7912c8ad287316>::operator()
0x00007ff709ce9bf2 (Unity) asio::detail::completion_handler<core::mutable_function<void __cdecl(void)>,asio::io_context::basic_executor_type<std::allocator<void>,0> >::do_complete
0x00007ff709cd5a92 (Unity) asio::detail::win_iocp_io_context::do_one
0x00007ff709cd79a4 (Unity) asio::detail::win_iocp_io_context::run
0x00007ff709ce7f2f (Unity) IOService::Run
0x00007ff709c83bfb (Unity) RunAssetImportWorkerClientV2
0x00007ff709c83c50 (Unity) RunAssetImporterV2
0x00007ff7096f6bd8 (Unity) Application::InitializeProject
0x00007ff709b79612 (Unity) WinMain
0x00007ff70b24765e (Unity) __scrt_common_main_seh
0x00007fffcec8259d (KERNEL32) BaseThreadInitThunk
0x00007fffcf08af78 (ntdll) RtlUserThreadStart

 -> (artifact id: 'dc1346a78a262b19532bb1742471b937') in 0.380031 seconds 
========================================================================
Received Import Request.
  Time since last request: 103.542602 seconds.
  path: Assets/Resources/Data/Enemy/familiar.asset
  artifactKey: Guid(f794bb34cae81244fb04b83eb5406a05) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/Enemy/familiar.asset using Guid(f794bb34cae81244fb04b83eb5406a05) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ea9eb724ae731c67a3f956c316c03096') in 0.004213 seconds 
========================================================================
Received Import Request.
  Time since last request: 10.651749 seconds.
  path: Assets/Resources/Data/Player/Alice.asset
  artifactKey: Guid(40ce26f1d82f30d4c9c7abde19141087) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/Player/Alice.asset using Guid(40ce26f1d82f30d4c9c7abde19141087) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4741cba2d0e372a52e306c924623fe2a') in 0.002888 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014215 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.32 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.252 seconds
Domain Reload Profiling:
	ReloadAssembly (2253ms)
		BeginReloadAssembly (213ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (82ms)
		EndReloadAssembly (1977ms)
			LoadAssemblies (215ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (516ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (96ms)
			SetupLoadedEditorAssemblies (1003ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (4ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (155ms)
				ProcessInitializeOnLoadAttributes (822ms)
				ProcessInitializeOnLoadMethodAttributes (17ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (16ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.08 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 273.7 MB.
System memory in use after: 274.2 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6939.
Total: 3.966800 ms (FindLiveObjects: 0.331900 ms CreateObjectMapping: 0.164800 ms MarkObjects: 3.435100 ms  DeleteObjects: 0.034000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 138.235327 seconds.
  path: Assets/Resources/Data/BuffData/24.asset
  artifactKey: Guid(7973a45838741314cab97824693597cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/24.asset using Guid(7973a45838741314cab97824693597cd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c36f0edf8afb7bca90c72a5b923c18a3') in 0.016442 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013755 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.963 seconds
Domain Reload Profiling:
	ReloadAssembly (1964ms)
		BeginReloadAssembly (187ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (84ms)
		EndReloadAssembly (1722ms)
			LoadAssemblies (188ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (472ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (87ms)
			SetupLoadedEditorAssemblies (863ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (158ms)
				ProcessInitializeOnLoadAttributes (672ms)
				ProcessInitializeOnLoadMethodAttributes (27ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (13ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 273.8 MB.
System memory in use after: 274.3 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6942.
Total: 3.570500 ms (FindLiveObjects: 0.313700 ms CreateObjectMapping: 0.168200 ms MarkObjects: 3.060800 ms  DeleteObjects: 0.026800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 69.939763 seconds.
  path: Assets/Resources/Data/BuffData/2001.asset
  artifactKey: Guid(e499df42ca75c3941a96d1be999785ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2001.asset using Guid(e499df42ca75c3941a96d1be999785ef) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6918c7dc7745324aa1db7d252b50a5d3') in 0.016294 seconds 
========================================================================
Received Import Request.
  Time since last request: 239.167243 seconds.
  path: Assets/Resources/Data/BuffData/26.asset
  artifactKey: Guid(890fa3bf3d46f334e94abb0dc368f565) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/26.asset using Guid(890fa3bf3d46f334e94abb0dc368f565) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '142b8b29e8e5d4335c2a9bdfb93f1fa6') in 0.004188 seconds 
========================================================================
Received Import Request.
  Time since last request: 154.689185 seconds.
  path: Assets/Scripts/SkillSystem/ImpactEffects/AddBuffImpact.cs
  artifactKey: Guid(cd184213c74cfb04790d993fa5a499df) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/SkillSystem/ImpactEffects/AddBuffImpact.cs using Guid(cd184213c74cfb04790d993fa5a499df) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7742568e4db4f1c903b1ce4785dac3af') in 0.008403 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.012471 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.85 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.105 seconds
Domain Reload Profiling:
	ReloadAssembly (1106ms)
		BeginReloadAssembly (105ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (37ms)
		EndReloadAssembly (966ms)
			LoadAssemblies (98ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (276ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (48ms)
			SetupLoadedEditorAssemblies (474ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (81ms)
				ProcessInitializeOnLoadAttributes (379ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.03 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 273.8 MB.
System memory in use after: 274.3 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6945.
Total: 3.622200 ms (FindLiveObjects: 0.327500 ms CreateObjectMapping: 0.182600 ms MarkObjects: 3.083900 ms  DeleteObjects: 0.027400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 34.352097 seconds.
  path: Assets/Scripts/SkillSystem/ImpactEffects/AddBuffImpact.cs
  artifactKey: Guid(cd184213c74cfb04790d993fa5a499df) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/SkillSystem/ImpactEffects/AddBuffImpact.cs using Guid(cd184213c74cfb04790d993fa5a499df) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '91bb2c4913e3fb3f2584b6ca57c2e9e4') in 0.006519 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.006560 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.124 seconds
Domain Reload Profiling:
	ReloadAssembly (1124ms)
		BeginReloadAssembly (100ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (32ms)
		EndReloadAssembly (988ms)
			LoadAssemblies (101ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (286ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (49ms)
			SetupLoadedEditorAssemblies (478ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (82ms)
				ProcessInitializeOnLoadAttributes (378ms)
				ProcessInitializeOnLoadMethodAttributes (13ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.98 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 273.9 MB.
System memory in use after: 274.4 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6948.
Total: 4.038400 ms (FindLiveObjects: 0.348200 ms CreateObjectMapping: 0.179500 ms MarkObjects: 3.469800 ms  DeleteObjects: 0.039000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014825 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.104 seconds
Domain Reload Profiling:
	ReloadAssembly (1104ms)
		BeginReloadAssembly (101ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (34ms)
		EndReloadAssembly (963ms)
			LoadAssemblies (102ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (274ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (48ms)
			SetupLoadedEditorAssemblies (471ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (99ms)
				ProcessInitializeOnLoadAttributes (358ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 273.9 MB.
System memory in use after: 274.4 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6951.
Total: 3.577200 ms (FindLiveObjects: 0.342500 ms CreateObjectMapping: 0.181600 ms MarkObjects: 3.026800 ms  DeleteObjects: 0.025600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 103.293808 seconds.
  path: Assets/Resources/Data/LevelUpData/24.asset
  artifactKey: Guid(02154d88aa3770147a02f02038c61230) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/24.asset using Guid(02154d88aa3770147a02f02038c61230) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'efa87f020fc38f504c9beb27d07d6d6d') in 0.014268 seconds 
========================================================================
Received Import Request.
  Time since last request: 202.334870 seconds.
  path: Assets/Scripts/Buff/BuffManager.cs
  artifactKey: Guid(93919e18c928a3b428dd6755217c9670) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Buff/BuffManager.cs using Guid(93919e18c928a3b428dd6755217c9670) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '003fd800040b85bbc96bb2a94cb15395') in 0.002865 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.021058 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.082 seconds
Domain Reload Profiling:
	ReloadAssembly (1082ms)
		BeginReloadAssembly (104ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (36ms)
		EndReloadAssembly (945ms)
			LoadAssemblies (101ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (277ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (47ms)
			SetupLoadedEditorAssemblies (452ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (79ms)
				ProcessInitializeOnLoadAttributes (358ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 273.9 MB.
System memory in use after: 274.4 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6954.
Total: 3.652400 ms (FindLiveObjects: 0.314100 ms CreateObjectMapping: 0.171400 ms MarkObjects: 3.138300 ms  DeleteObjects: 0.027800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 35.380146 seconds.
  path: Assets/Scripts/Buff/BuffManager.cs
  artifactKey: Guid(93919e18c928a3b428dd6755217c9670) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Buff/BuffManager.cs using Guid(93919e18c928a3b428dd6755217c9670) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'eb28d23b6347bea532fe21b0f3863905') in 0.006079 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.011626 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.136 seconds
Domain Reload Profiling:
	ReloadAssembly (1137ms)
		BeginReloadAssembly (112ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (40ms)
		EndReloadAssembly (989ms)
			LoadAssemblies (119ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (274ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (49ms)
			SetupLoadedEditorAssemblies (469ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (80ms)
				ProcessInitializeOnLoadAttributes (373ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.09 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 273.9 MB.
System memory in use after: 274.4 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6957.
Total: 3.513400 ms (FindLiveObjects: 0.319000 ms CreateObjectMapping: 0.179500 ms MarkObjects: 2.986500 ms  DeleteObjects: 0.027700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 52.556256 seconds.
  path: Assets/Scripts/Buff/BuffManager.cs
  artifactKey: Guid(93919e18c928a3b428dd6755217c9670) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Buff/BuffManager.cs using Guid(93919e18c928a3b428dd6755217c9670) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f957369cd0394eba76c24fb7be49ca2c') in 0.006870 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013016 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.10 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.144 seconds
Domain Reload Profiling:
	ReloadAssembly (1144ms)
		BeginReloadAssembly (106ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (34ms)
		EndReloadAssembly (1002ms)
			LoadAssemblies (108ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (293ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (50ms)
			SetupLoadedEditorAssemblies (476ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (83ms)
				ProcessInitializeOnLoadAttributes (375ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 273.9 MB.
System memory in use after: 274.4 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6960.
Total: 3.590100 ms (FindLiveObjects: 0.312800 ms CreateObjectMapping: 0.173600 ms MarkObjects: 3.077200 ms  DeleteObjects: 0.025700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 32.909147 seconds.
  path: Assets/Resources/Data/SkillData/26.asset
  artifactKey: Guid(b6c45e763b12112409c01ac81a507b12) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/26.asset using Guid(b6c45e763b12112409c01ac81a507b12) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '854ea0b2044785c8f5bf195621a53419') in 0.017061 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000170 seconds.
  path: Assets/Resources/Data/LevelUpData/132.asset
  artifactKey: Guid(6f44807ba5ece8e4bbb1fe29561efe02) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/132.asset using Guid(6f44807ba5ece8e4bbb1fe29561efe02) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd7ae3314dac91f0b301cdb234a7d5c41') in 0.003559 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000161 seconds.
  path: Assets/Resources/Data/LevelUpData/124.asset
  artifactKey: Guid(815c5f345a058a64abd4f5a0ae7a4919) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/124.asset using Guid(815c5f345a058a64abd4f5a0ae7a4919) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1678018f156f3f5470c8e3803bbedc3e') in 0.003214 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000151 seconds.
  path: Assets/Resources/Data/BuffData/2004.asset
  artifactKey: Guid(8e52c1a1e7c74eb41be5993df48e2821) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2004.asset using Guid(8e52c1a1e7c74eb41be5993df48e2821) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '45376cfa6e97e6f5eb043e0b607658af') in 0.003916 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000198 seconds.
  path: Assets/Resources/Data/BuffData/27.asset
  artifactKey: Guid(47d8f82bc0c3107439dd6e7d218a76e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/27.asset using Guid(47d8f82bc0c3107439dd6e7d218a76e5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd90cd4a5b7311b9a4620acae72305e8c') in 0.003747 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000157 seconds.
  path: Assets/Resources/Data/LevelUpData/20.asset
  artifactKey: Guid(6f2289b00cf050b4cb8cdcff02b98640) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/20.asset using Guid(6f2289b00cf050b4cb8cdcff02b98640) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a10a6ffca189cb995ae8646d2bc97906') in 0.003680 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000182 seconds.
  path: Assets/Resources/Data/BuffData/2029.asset
  artifactKey: Guid(7c676f00b9dc4d14cbe5c616889d0cac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2029.asset using Guid(7c676f00b9dc4d14cbe5c616889d0cac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '53630f573c06ffdda33bd2ee913ca1f3') in 0.003321 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000196 seconds.
  path: Assets/Resources/Data/BuffData/142.asset
  artifactKey: Guid(1b11948b3f71ece408d620f80089b795) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/142.asset using Guid(1b11948b3f71ece408d620f80089b795) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a368788b0ec3456362ab92859dbc1efb') in 0.003129 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000173 seconds.
  path: Assets/Resources/Data/LevelUpData/129.asset
  artifactKey: Guid(3894fd6fc9cc36940b02e30028167bc6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/129.asset using Guid(3894fd6fc9cc36940b02e30028167bc6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8b3704c0645985de4ad3789974cbd276') in 0.004435 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000140 seconds.
  path: Assets/Resources/Data/SkillData/3024.asset
  artifactKey: Guid(62e7c6ed3cc8d814e9d097ffbec92019) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/3024.asset using Guid(62e7c6ed3cc8d814e9d097ffbec92019) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '95942e166a4c64eb97fc17750f536a40') in 0.003404 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000214 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect24.png
  artifactKey: Guid(5e0a9db568fc4e64cb7306fd68d6f18d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect24.png using Guid(5e0a9db568fc4e64cb7306fd68d6f18d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '105d1ae006c45be3257a28d02c8fef71') in 0.062622 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000152 seconds.
  path: Assets/Resources/Data/BuffData/2011.asset
  artifactKey: Guid(94fc46743c27d9d4ba78fa35360cf274) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2011.asset using Guid(94fc46743c27d9d4ba78fa35360cf274) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5d4da46c0febd5e49544cef97caa2189') in 0.003459 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000156 seconds.
  path: Assets/Resources/Data/BuffData/2012.asset
  artifactKey: Guid(6fe40c7eedf9ef7478cf1ab7835806ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2012.asset using Guid(6fe40c7eedf9ef7478cf1ab7835806ad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3bf0df1ff6074360ae25117b4b659b38') in 0.002915 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000144 seconds.
  path: Assets/Resources/Data/BuffData/2018.asset
  artifactKey: Guid(168df469deb69e6428790994d2ac38dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2018.asset using Guid(168df469deb69e6428790994d2ac38dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3c7b84ba8416929f49e5aa34c87a5837') in 0.003480 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000263 seconds.
  path: Assets/Resources/Data/BuffData/2019.asset
  artifactKey: Guid(339b129bf2408b74781145e6f8432259) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2019.asset using Guid(339b129bf2408b74781145e6f8432259) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '11722ee6dffb270554fa41843a350a29') in 0.003360 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000198 seconds.
  path: Assets/Resources/Data/SkillData/42.asset
  artifactKey: Guid(e88a3a05e3e43c940a18430980ab8004) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/42.asset using Guid(e88a3a05e3e43c940a18430980ab8004) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f78a53a225591c961f437266fe361b54') in 0.002731 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000196 seconds.
  path: Assets/Resources/Data/LevelUpData/3024.asset
  artifactKey: Guid(6a7af895978f24e409c71a3e0c023720) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3024.asset using Guid(6a7af895978f24e409c71a3e0c023720) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b01bc4fd7eabcaf07449ea72d93561ec') in 0.002853 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000163 seconds.
  path: Assets/Resources/Data/BuffData/2008.asset
  artifactKey: Guid(8f909c39e8f69ae44ba422e3c9df4ad5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2008.asset using Guid(8f909c39e8f69ae44ba422e3c9df4ad5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '99ffc24018f5dda13a8ce275919ab11b') in 0.003843 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000453 seconds.
  path: Assets/Resources/Data/SkillData/129.asset
  artifactKey: Guid(ec32852ecbdaf81419f077d31c722ab4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/129.asset using Guid(ec32852ecbdaf81419f077d31c722ab4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4a2ac27b14125dcc969e2b487292b3d4') in 0.003091 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000164 seconds.
  path: Assets/Resources/Data/SkillData/127.asset
  artifactKey: Guid(1e0ced76ba9a6584088df53f0e97359c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/127.asset using Guid(1e0ced76ba9a6584088df53f0e97359c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b73d6b162ad76555af0cebaefbd72d04') in 0.003785 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000225 seconds.
  path: Assets/Resources/Data/BuffData/128.asset
  artifactKey: Guid(13998b90358c1c349a90971f9e084c42) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/128.asset using Guid(13998b90358c1c349a90971f9e084c42) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1e0f4823d368600ddeff07afb7f80069') in 0.003544 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000190 seconds.
  path: Assets/Resources/Data/OutsceneBuffData/24.asset
  artifactKey: Guid(03091125dbe79664aa25c2792f855026) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/OutsceneBuffData/24.asset using Guid(03091125dbe79664aa25c2792f855026) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dfa5071810e42740f5f486cf0edb51a5') in 0.003486 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000156 seconds.
  path: Assets/Resources/Data/BuffData/62.asset
  artifactKey: Guid(b05eb1df1f2a19a4bbb7b5418e77d459) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/62.asset using Guid(b05eb1df1f2a19a4bbb7b5418e77d459) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f695709856c4a4311a87fa0e0a1ea9ed') in 0.004021 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000146 seconds.
  path: Assets/Resources/Data/BuffData/2016.asset
  artifactKey: Guid(3e2db977c0af85a468d53ffeeb2c3451) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2016.asset using Guid(3e2db977c0af85a468d53ffeeb2c3451) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f55af7a51035d8cd3e3740a3507600f0') in 0.003688 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000248 seconds.
  path: Assets/Resources/Data/SkillData/82.asset
  artifactKey: Guid(ab64801275606e44181579ef5fbc0255) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/82.asset using Guid(ab64801275606e44181579ef5fbc0255) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3af4bdabf149172fdbbe35d7ead5d1c8') in 0.003728 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000190 seconds.
  path: Assets/Resources/Data/LevelUpData/82.asset
  artifactKey: Guid(76c095531b83a7e48aed34a73da21839) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/82.asset using Guid(76c095531b83a7e48aed34a73da21839) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a02a5c4dca6fe52dd8f3191cbfdd9345') in 0.002486 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000156 seconds.
  path: Assets/Resources/Data/LevelUpData/27.asset
  artifactKey: Guid(d51c86ab66f7efc40b06b57bb8c62046) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/27.asset using Guid(d51c86ab66f7efc40b06b57bb8c62046) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '16a68020178c777738890e10dd7eb229') in 0.002672 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000328 seconds.
  path: Assets/Resources/Data/SkillData/20.asset
  artifactKey: Guid(a3f8d652dd8f8fd4a8883ad039099bbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/20.asset using Guid(a3f8d652dd8f8fd4a8883ad039099bbc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '00ded97442a9df955fa0790ec3ebc9e9') in 0.003008 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000395 seconds.
  path: Assets/Resources/Data/LevelUpData/28.asset
  artifactKey: Guid(38fe5e269b89dcd4c963a9994271aee5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/28.asset using Guid(38fe5e269b89dcd4c963a9994271aee5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a79cddf7990162c28f154cd1f23ee18d') in 0.002735 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000163 seconds.
  path: Assets/Resources/Data/LevelUpData/29.asset
  artifactKey: Guid(94a985926d748c4418047c014f0adc18) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/29.asset using Guid(94a985926d748c4418047c014f0adc18) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '663565b578d49f4bc23611e5853c6a18') in 0.003602 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000163 seconds.
  path: Assets/Resources/Data/OutsceneBuffData/20.asset
  artifactKey: Guid(7f71872552eb1d24296b80a35fdf2400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/OutsceneBuffData/20.asset using Guid(7f71872552eb1d24296b80a35fdf2400) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '893cb357ca6a3aed894aca3f7def93a6') in 0.002670 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000148 seconds.
  path: Assets/Resources/Data/BuffData/32.asset
  artifactKey: Guid(5dff81e0a4680bb4c87ccb32e9a96ab9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/32.asset using Guid(5dff81e0a4680bb4c87ccb32e9a96ab9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd3ed3fc2c5c63022257d19b4fe06377c') in 0.002783 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000351 seconds.
  path: Assets/Resources/Data/BuffData/129.asset
  artifactKey: Guid(9603e6a47198f284e88a99df6a46bd7d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/129.asset using Guid(9603e6a47198f284e88a99df6a46bd7d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b0bdbbf4abc041f21390d9f786636f40') in 0.002759 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000161 seconds.
  path: Assets/Resources/Data/BuffData/82.asset
  artifactKey: Guid(7850511a33c148943995cc4e1e641b46) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/82.asset using Guid(7850511a33c148943995cc4e1e641b46) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7da6912fa667b76c8540cb7db1511a9d') in 0.002558 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000156 seconds.
  path: Assets/Resources/Data/BuffData/2030.asset
  artifactKey: Guid(7a14325848d5d3949abbc09610686729) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2030.asset using Guid(7a14325848d5d3949abbc09610686729) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '91781b35a1aa1ab3bce61de41e2f8a25') in 0.003168 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000353 seconds.
  path: Assets/Resources/Data/BuffData/2020.asset
  artifactKey: Guid(c00ef352e56de154c8ccd01a66060bb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2020.asset using Guid(c00ef352e56de154c8ccd01a66060bb9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '2b771ab6384b6b12ca63f5ab3aec2fa6') in 0.003366 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000171 seconds.
  path: Assets/Resources/Data/SkillData/29.asset
  artifactKey: Guid(54e746d1e3f70bd43abbc11e1347322f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/29.asset using Guid(54e746d1e3f70bd43abbc11e1347322f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8114e767d4e9bcdf802aed5bb06683d6') in 0.002860 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000165 seconds.
  path: Assets/Resources/Data/SkillData/27.asset
  artifactKey: Guid(12e8200995067fd47b1e2d6b253f21b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/27.asset using Guid(12e8200995067fd47b1e2d6b253f21b3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f1c6cf7460886f818dabaaea34619e8d') in 0.003002 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000243 seconds.
  path: Assets/Resources/Data/LevelUpData/127.asset
  artifactKey: Guid(04295252bdbb10e49b607bf743334960) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/127.asset using Guid(04295252bdbb10e49b607bf743334960) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b71a79a4a7e033584cd1bfd876e117e1') in 0.003044 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000193 seconds.
  path: Assets/Resources/Data/LevelUpData/26.asset
  artifactKey: Guid(11d87a96db4819a42a04f6d1002225a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/26.asset using Guid(11d87a96db4819a42a04f6d1002225a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a6b0c1b803c4a4f0f71363f7f8849b72') in 0.003577 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000203 seconds.
  path: Assets/Resources/Data/SkillData/72.asset
  artifactKey: Guid(713a4c8df9a169b4a934d5b8a80d21a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/72.asset using Guid(713a4c8df9a169b4a934d5b8a80d21a2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8f283a22bf38378f7dcaf6b705a5b0e4') in 0.002759 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000151 seconds.
  path: Assets/Resources/Data/SkillData/124.asset
  artifactKey: Guid(603e6d9a612bd984aa3faec136e40624) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/124.asset using Guid(603e6d9a612bd984aa3faec136e40624) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f966e185c89de619070bdad722155747') in 0.003386 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000192 seconds.
  path: Assets/Resources/Data/BuffData/2003.asset
  artifactKey: Guid(cf073f4cb616e7941be861d60956ee72) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2003.asset using Guid(cf073f4cb616e7941be861d60956ee72) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'eb4057bf724c6cff6c8cd38582cf5cd1') in 0.003533 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000279 seconds.
  path: Assets/Resources/Data/BuffData/2032.asset
  artifactKey: Guid(db039c46e2b61c74ba49362d304174a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2032.asset using Guid(db039c46e2b61c74ba49362d304174a1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '105aaf95eb20c863d3543b0d59ba59c7') in 0.003413 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000166 seconds.
  path: Assets/Resources/Data/BuffData/2014.asset
  artifactKey: Guid(1349f37ba566d8148b10b1a651cf54f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2014.asset using Guid(1349f37ba566d8148b10b1a651cf54f9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8ace5fd67cfd3566998dcfc64366dabf') in 0.003197 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000205 seconds.
  path: Assets/Resources/Data/LevelUpData/32.asset
  artifactKey: Guid(0cba598b0e04b61498122aa8cfbbf80c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/32.asset using Guid(0cba598b0e04b61498122aa8cfbbf80c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4cb9b11bbec27b27163646253392870c') in 0.002786 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000180 seconds.
  path: Assets/Resources/Data/LevelUpData/23.asset
  artifactKey: Guid(15727737d1c746e4eb8b1087f7d67fac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/23.asset using Guid(15727737d1c746e4eb8b1087f7d67fac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5306475a43bee3ae6a1fd2e7f96c39ec') in 0.002752 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000149 seconds.
  path: Assets/Resources/Data/BuffData/1012.asset
  artifactKey: Guid(e37ceef69b671f74b89c4f137ca7e6b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/1012.asset using Guid(e37ceef69b671f74b89c4f137ca7e6b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cf7c8c77c0c2aecdf396d02f6d02263f') in 0.003050 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000224 seconds.
  path: Assets/Resources/Data/BuffData/2007.asset
  artifactKey: Guid(5f1563d2adcde4c4db3c561e836c3b88) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2007.asset using Guid(5f1563d2adcde4c4db3c561e836c3b88) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3028662ddcffbec511410f733ab88a22') in 0.003642 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000172 seconds.
  path: Assets/Resources/Data/SkillData/128.asset
  artifactKey: Guid(b7fb791d28653264a973cba6d3dd0fec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/128.asset using Guid(b7fb791d28653264a973cba6d3dd0fec) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1030d35d988e6aa5393069dfe078b7d2') in 0.002849 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000159 seconds.
  path: Assets/Resources/Data/BuffData/1021.asset
  artifactKey: Guid(c60b17ace96c054498d4fd2abaa14d1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/1021.asset using Guid(c60b17ace96c054498d4fd2abaa14d1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '21d14626fbefcb580953d0541c2ecb19') in 0.003171 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000268 seconds.
  path: Assets/Resources/Data/BuffData/2009.asset
  artifactKey: Guid(e89792f3e050b0a40a57697f1ed8c8cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2009.asset using Guid(e89792f3e050b0a40a57697f1ed8c8cf) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1b3206963fa8bfbcb572af1acedb4273') in 0.003840 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000206 seconds.
  path: Assets/Resources/Data/BuffData/126.asset
  artifactKey: Guid(7a86edb39b8910b42a4c93b6243cdc66) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/126.asset using Guid(7a86edb39b8910b42a4c93b6243cdc66) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd80dbab0f230751709df8baed617b016') in 0.003924 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000237 seconds.
  path: Assets/Resources/Data/BuffData/127.asset
  artifactKey: Guid(a1c2f03d32164a6468d643ab4dd05a78) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/127.asset using Guid(a1c2f03d32164a6468d643ab4dd05a78) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '103f4eea0a197339350c9037481e6f99') in 0.002651 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000187 seconds.
  path: Assets/Resources/Data/BuffData/2031.asset
  artifactKey: Guid(e297812a2c3db4b4a888b703f29fc319) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2031.asset using Guid(e297812a2c3db4b4a888b703f29fc319) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '188321be58f47845e6e444cb672089ae') in 0.004627 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000201 seconds.
  path: Assets/Resources/Data/BuffData/72.asset
  artifactKey: Guid(6ec0c2f71ce0a2647a78969c8b83c648) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/72.asset using Guid(6ec0c2f71ce0a2647a78969c8b83c648) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '86455c82df127a0334de6fbba6f418c5') in 0.003290 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000224 seconds.
  path: Assets/Resources/Data/BuffData/2006.asset
  artifactKey: Guid(b03a04e79277d2545bd38984ec1b2ff9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2006.asset using Guid(b03a04e79277d2545bd38984ec1b2ff9) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7a7e7f8d79277c4ca172466aba77e813') in 0.004098 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000183 seconds.
  path: Assets/Resources/Data/OutsceneBuffData/22.asset
  artifactKey: Guid(a9e0bf58c7bf10342a9c3bea82230ff4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/OutsceneBuffData/22.asset using Guid(a9e0bf58c7bf10342a9c3bea82230ff4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '336e1b89773ff26ba7ab6ae7f3e06580') in 0.002715 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000195 seconds.
  path: Assets/Resources/Data/LevelUpData/62.asset
  artifactKey: Guid(2f837a07baa1c7c4c8128518e1a50bd6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/62.asset using Guid(2f837a07baa1c7c4c8128518e1a50bd6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '418075663812822dead601c668aa050f') in 0.003132 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000218 seconds.
  path: Assets/Resources/Data/SkillData/62.asset
  artifactKey: Guid(f9bb9e099195dbb419194cad3d6da40e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/62.asset using Guid(f9bb9e099195dbb419194cad3d6da40e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fddaba79fe5113109e77c208f015c3c8') in 0.003283 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000237 seconds.
  path: Assets/Resources/Data/BuffData/2022.asset
  artifactKey: Guid(76051987f5086474cbfc1b0060d56bad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2022.asset using Guid(76051987f5086474cbfc1b0060d56bad) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '76be53a3d6fb725c0e6bf8e3ced00880') in 0.003129 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000179 seconds.
  path: Assets/Art/Sprites/Tower/windtower_attack/windtower_gif24.png
  artifactKey: Guid(97170f2567b7ab34795afe9f22987b10) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/windtower_attack/windtower_gif24.png using Guid(97170f2567b7ab34795afe9f22987b10) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f9d3f323c9da4637f21531d048e836d1') in 0.013977 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000212 seconds.
  path: Assets/Resources/Data/OutsceneBuffData/23.asset
  artifactKey: Guid(2504c63bb38bfe245abe9011bea4b992) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/OutsceneBuffData/23.asset using Guid(2504c63bb38bfe245abe9011bea4b992) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '35ea85666fa526b25c9a91ad049aa4a8') in 0.003392 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000254 seconds.
  path: Assets/Resources/Data/LevelUpData/92.asset
  artifactKey: Guid(90fbed1884392174e87f5d07de123f66) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/92.asset using Guid(90fbed1884392174e87f5d07de123f66) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '742c2aae208a949b200452acdda0d3b0') in 0.003485 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000174 seconds.
  path: Assets/Resources/Data/BuffData/2024.asset
  artifactKey: Guid(535a14f03caeda84daeab5ff33a25aa8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2024.asset using Guid(535a14f03caeda84daeab5ff33a25aa8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '575dd0b8bf73457bc5a9e3a21959ad14') in 0.004414 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000291 seconds.
  path: Assets/Resources/Data/LevelUpData/142.asset
  artifactKey: Guid(d52136e7f87e9a84abc27df73ef46fed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/142.asset using Guid(d52136e7f87e9a84abc27df73ef46fed) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b10783e37bebf526012df99eb4dbe6b7') in 0.003507 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000316 seconds.
  path: Assets/Resources/Data/LevelUpData/42.asset
  artifactKey: Guid(086b6bb439df5314ba307d520eb5cff1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/42.asset using Guid(086b6bb439df5314ba307d520eb5cff1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '64becc5e884db6f90a265d98be557733') in 0.002900 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000278 seconds.
  path: Assets/Resources/Data/BuffData/2027.asset
  artifactKey: Guid(6f7239835a910f944939bac623b7ea8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2027.asset using Guid(6f7239835a910f944939bac623b7ea8d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3cbda229bcf30ae2eeb803ee3bc32b58') in 0.003625 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000258 seconds.
  path: Assets/Resources/Data/LevelUpData/128.asset
  artifactKey: Guid(d233d41c2837bd14781cf47ed9f4b4f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/128.asset using Guid(d233d41c2837bd14781cf47ed9f4b4f8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6bb5d48771018e434dad25a7aadb9e9f') in 0.003339 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000272 seconds.
  path: Assets/Resources/Data/SkillData/92.asset
  artifactKey: Guid(3da6763c76c4f764c805a568eccea8ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/92.asset using Guid(3da6763c76c4f764c805a568eccea8ce) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '85a462bdb317430151f62dfe83d9a342') in 0.003741 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000159 seconds.
  path: Assets/Resources/Data/BuffData/92.asset
  artifactKey: Guid(9e1645f6a9debdd43b89adbf7cbdba58) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/92.asset using Guid(9e1645f6a9debdd43b89adbf7cbdba58) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd0d0b1511ba21e85a85d8bfaf43f29c5') in 0.002552 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000206 seconds.
  path: Assets/Resources/Data/LevelUpData/126.asset
  artifactKey: Guid(80d0ccfea39f7c643a3053164239d23c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/126.asset using Guid(80d0ccfea39f7c643a3053164239d23c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fd1a5d9527517e91c0cfabe17fa45ec8') in 0.002806 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000172 seconds.
  path: Assets/Resources/Data/BuffData/124.asset
  artifactKey: Guid(98a02b53db1e7fd469bdb688635c835e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/124.asset using Guid(98a02b53db1e7fd469bdb688635c835e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3fd6020f7f5fe573bc2bbc577d59e58a') in 0.003083 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000376 seconds.
  path: Assets/Resources/Data/BuffData/29.asset
  artifactKey: Guid(07efad3507f4cc541bf03cd2fdc44156) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/29.asset using Guid(07efad3507f4cc541bf03cd2fdc44156) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8b5027c66a6eed0605940f0cabff81e1') in 0.002572 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.019178 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.165 seconds
Domain Reload Profiling:
	ReloadAssembly (2166ms)
		BeginReloadAssembly (148ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (45ms)
		EndReloadAssembly (1955ms)
			LoadAssemblies (197ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (521ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (86ms)
			SetupLoadedEditorAssemblies (948ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (4ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (152ms)
				ProcessInitializeOnLoadAttributes (769ms)
				ProcessInitializeOnLoadMethodAttributes (16ms)
				AfterProcessingInitializeOnLoad (5ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (26ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.0 MB.
System memory in use after: 274.5 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6966.
Total: 5.824200 ms (FindLiveObjects: 0.845000 ms CreateObjectMapping: 0.283600 ms MarkObjects: 4.648400 ms  DeleteObjects: 0.046000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018305 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.081 seconds
Domain Reload Profiling:
	ReloadAssembly (1081ms)
		BeginReloadAssembly (100ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (34ms)
		EndReloadAssembly (947ms)
			LoadAssemblies (98ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (268ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (47ms)
			SetupLoadedEditorAssemblies (463ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (78ms)
				ProcessInitializeOnLoadAttributes (370ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.12 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.0 MB.
System memory in use after: 274.5 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6969.
Total: 3.640600 ms (FindLiveObjects: 0.336100 ms CreateObjectMapping: 0.168500 ms MarkObjects: 3.108900 ms  DeleteObjects: 0.026300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.009787 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.26 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.418 seconds
Domain Reload Profiling:
	ReloadAssembly (2419ms)
		BeginReloadAssembly (181ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (46ms)
		EndReloadAssembly (2176ms)
			LoadAssemblies (236ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (639ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (93ms)
			SetupLoadedEditorAssemblies (1051ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (206ms)
				ProcessInitializeOnLoadAttributes (817ms)
				ProcessInitializeOnLoadMethodAttributes (19ms)
				AfterProcessingInitializeOnLoad (4ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (31ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.16 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.0 MB.
System memory in use after: 274.5 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6972.
Total: 3.567800 ms (FindLiveObjects: 0.326400 ms CreateObjectMapping: 0.172400 ms MarkObjects: 3.039400 ms  DeleteObjects: 0.028800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.005936 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.04 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.117 seconds
Domain Reload Profiling:
	ReloadAssembly (1117ms)
		BeginReloadAssembly (96ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (32ms)
		EndReloadAssembly (987ms)
			LoadAssemblies (100ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (286ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (49ms)
			SetupLoadedEditorAssemblies (475ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (84ms)
				ProcessInitializeOnLoadAttributes (375ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.0 MB.
System memory in use after: 274.5 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6975.
Total: 3.705000 ms (FindLiveObjects: 0.340000 ms CreateObjectMapping: 0.170700 ms MarkObjects: 3.157300 ms  DeleteObjects: 0.035800 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 458.346014 seconds.
  path: Assets/Resources/Data/OutsceneBuffData/6.asset
  artifactKey: Guid(2139f5bfccb32f74883f7ec85cf9c993) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/OutsceneBuffData/6.asset using Guid(2139f5bfccb32f74883f7ec85cf9c993) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4dfa4822c4ddf7a7ada8c0124b4146a9') in 0.017507 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000223 seconds.
  path: Assets/Resources/Data/SkillData/36.asset
  artifactKey: Guid(24d19086c976da04997891f784babe11) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/36.asset using Guid(24d19086c976da04997891f784babe11) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8bb15b9b9774f48067ffb9245ebf5091') in 0.004543 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000139 seconds.
  path: Assets/Resources/Data/LevelUpData/46.asset
  artifactKey: Guid(5b658039ae5154f4797d4cb83ccf7404) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/46.asset using Guid(5b658039ae5154f4797d4cb83ccf7404) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '80ec713a123df5c4bcb9814008a267a5') in 0.003615 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000159 seconds.
  path: Assets/Resources/Data/SkillData/60.asset
  artifactKey: Guid(b20ac425d1cce97448ccd5af007e2941) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/60.asset using Guid(b20ac425d1cce97448ccd5af007e2941) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '629b045efd256bb5907ae82a2abc6b36') in 0.003436 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000194 seconds.
  path: Assets/Resources/Data/LevelUpData/60.asset
  artifactKey: Guid(e2714293fa3e4bd499d73daa20ac6971) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/60.asset using Guid(e2714293fa3e4bd499d73daa20ac6971) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3a177e16f01b91b77fff9f709ca7af10') in 0.002410 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000131 seconds.
  path: Assets/Resources/Data/LevelUpData/64.asset
  artifactKey: Guid(72cef701d056e26498247089f741974e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/64.asset using Guid(72cef701d056e26498247089f741974e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'de879b930b99ed5c283e8d2c5f1cd843') in 0.003220 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000133 seconds.
  path: Assets/Resources/Data/LevelUpData/63.asset
  artifactKey: Guid(ec7abe92d55e7304f92d354eba6a966e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/63.asset using Guid(ec7abe92d55e7304f92d354eba6a966e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '84704bf677b1745a36a67e6a8cbb7637') in 0.003411 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000220 seconds.
  path: Assets/Resources/Data/SkillData/68.asset
  artifactKey: Guid(e0a95dfdd8f25e5449f3c3e33a0a566f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/68.asset using Guid(e0a95dfdd8f25e5449f3c3e33a0a566f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fcbafa1c753d5dad58d4aa03d4c2f250') in 0.003829 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000151 seconds.
  path: Assets/Resources/Data/SkillData/69.asset
  artifactKey: Guid(d6c676bb6d16ae94cb5ce1b9d9aaaacc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/69.asset using Guid(d6c676bb6d16ae94cb5ce1b9d9aaaacc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b96b59cf6bf352483efdb6e726ca7255') in 0.003595 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000173 seconds.
  path: Assets/Resources/Data/BuffData/76.asset
  artifactKey: Guid(ae15b2741ff2509418ded6649be30346) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/76.asset using Guid(ae15b2741ff2509418ded6649be30346) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '94b33bbebbcb77b69e18e5a9cfb3ba5e') in 0.004429 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000340 seconds.
  path: Assets/Resources/Data/BuffData/96.asset
  artifactKey: Guid(ce3bdf5069702cf4b8aacde4f3dae192) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/96.asset using Guid(ce3bdf5069702cf4b8aacde4f3dae192) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c6fd9df9f8e2c152fd62727347d2e498') in 0.003229 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000170 seconds.
  path: Assets/Resources/Data/BuffData/68.asset
  artifactKey: Guid(f32b66469bc6d2042b830223bd4c1e8a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/68.asset using Guid(f32b66469bc6d2042b830223bd4c1e8a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6fc66ff432a5e09d57e765ef3b1f57bf') in 0.002963 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000150 seconds.
  path: Assets/Resources/Data/LevelUpData/136.asset
  artifactKey: Guid(3931cb9dc47c4734dad772fc4c63936c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/136.asset using Guid(3931cb9dc47c4734dad772fc4c63936c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a3763e012d24e2bd8170f2a0793f569b') in 0.003497 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000170 seconds.
  path: Assets/Resources/Data/BuffData/1016.asset
  artifactKey: Guid(757758b42f595354d9a3ee621b4461d3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/1016.asset using Guid(757758b42f595354d9a3ee621b4461d3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '67b6f1a2098b14fed8e45677657e36d4') in 0.003796 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000173 seconds.
  path: Assets/Resources/Data/LevelUpData/146.asset
  artifactKey: Guid(1486e38653b92734c80c55adf278789a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/146.asset using Guid(1486e38653b92734c80c55adf278789a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f44266d877f95a78ef5adf58adbc1ad8') in 0.003348 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000151 seconds.
  path: Assets/Resources/Data/BuffData/3016.asset
  artifactKey: Guid(23021d9397ac500429979cb5ea316534) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/3016.asset using Guid(23021d9397ac500429979cb5ea316534) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f827cc469a219ef1d76f8eb240e11440') in 0.002816 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000209 seconds.
  path: Assets/Resources/Data/LevelUpData/3026.asset
  artifactKey: Guid(1c00c2601d8bb3347a3e4553558a31dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3026.asset using Guid(1c00c2601d8bb3347a3e4553558a31dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '224000057640def963b2eea2c02903b9') in 0.003408 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000221 seconds.
  path: Assets/Resources/Data/SkillData/3026.asset
  artifactKey: Guid(d4a43ddd288491446a601aff81b30b35) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/3026.asset using Guid(d4a43ddd288491446a601aff81b30b35) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '26906ceb662d6ffcc5ee0831311d164f') in 0.003179 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000149 seconds.
  path: Assets/Resources/Data/BuffData/3026.asset
  artifactKey: Guid(04fb762ee38302b448a8532d532ce85d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/3026.asset using Guid(04fb762ee38302b448a8532d532ce85d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '94a962b5103f30acb6aa9809617f7105') in 0.002606 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000198 seconds.
  path: Assets/Resources/Data/LevelUpData/3061.asset
  artifactKey: Guid(e06673c0e2529b841a2831db24b28862) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3061.asset using Guid(e06673c0e2529b841a2831db24b28862) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '978bc59395e7c0f37c31926198dac444') in 0.003099 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000130 seconds.
  path: Assets/Resources/Data/LevelUpData/3060.asset
  artifactKey: Guid(13223ac25f08a0b468570ea379466733) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3060.asset using Guid(13223ac25f08a0b468570ea379466733) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ebcbcdba8045c7466a53262683fe67fa') in 0.003138 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000163 seconds.
  path: Assets/Resources/Data/SkillData/63.asset
  artifactKey: Guid(1118ee3fd09bb1d4c85fe6d6e93328c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/63.asset using Guid(1118ee3fd09bb1d4c85fe6d6e93328c3) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f30a2c71977cde3b81ca5c455bf52eb4') in 0.002900 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000159 seconds.
  path: Assets/Resources/Data/BuffData/67.asset
  artifactKey: Guid(7a8ae817bdefb9e408fa28efde628b27) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/67.asset using Guid(7a8ae817bdefb9e408fa28efde628b27) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bca55db65c6541a72aea9f7f0e6c7c9a') in 0.003252 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000165 seconds.
  path: Assets/Resources/Data/BuffData/3067.asset
  artifactKey: Guid(fce31a2acdf6f2947bf43c73ab95d444) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/3067.asset using Guid(fce31a2acdf6f2947bf43c73ab95d444) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '628aeb4c10c15ac8e50c400bf5e0308d') in 0.003134 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000147 seconds.
  path: Assets/Resources/Data/LevelUpData/3069.asset
  artifactKey: Guid(a742ce8791bca2148b6088bdfd0a33c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3069.asset using Guid(a742ce8791bca2148b6088bdfd0a33c7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '58b7579c3cd18336c7a96cdf0584e175') in 0.003362 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000157 seconds.
  path: Assets/Resources/Data/BuffData/3086.asset
  artifactKey: Guid(94b25195d9188444fbee83c752f4f811) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/3086.asset using Guid(94b25195d9188444fbee83c752f4f811) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fa890f1ad9ebe5f8a65b48bf09afd033') in 0.003248 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000180 seconds.
  path: Assets/Art/Sprites/Tower/darktower_attack/darktower_attack16.png
  artifactKey: Guid(7b5043b185596f5489e67e229bdb7fc4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/darktower_attack/darktower_attack16.png using Guid(7b5043b185596f5489e67e229bdb7fc4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dacad73d8ba2a582149d258526cf68cd') in 0.027961 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000184 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect16.png
  artifactKey: Guid(3309b60e1c568094eada39abd7f0173f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect16.png using Guid(3309b60e1c568094eada39abd7f0173f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ed97c75a26d73e5b5fa3e016208108e7') in 0.009471 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000181 seconds.
  path: Assets/Resources/Data/BuffData/3046.asset
  artifactKey: Guid(8f606a3760b0ab646823f70b9a08b39f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/3046.asset using Guid(8f606a3760b0ab646823f70b9a08b39f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5eb245c5d5b3226f8594f318554b2fef') in 0.003565 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000140 seconds.
  path: Assets/Resources/Data/BuffData/86.asset
  artifactKey: Guid(9aff27df29963b649bba3ffe92e3cf77) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/86.asset using Guid(9aff27df29963b649bba3ffe92e3cf77) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8ea6be89f8097a51b7b2d9ee281281a6') in 0.002830 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000178 seconds.
  path: Assets/Resources/Data/BuffData/2036.asset
  artifactKey: Guid(90af3b6e65ded61428c030b906c6e5aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/2036.asset using Guid(90af3b6e65ded61428c030b906c6e5aa) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '429950f542a6eb1207204ae55f2bb420') in 0.003819 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000265 seconds.
  path: Assets/Resources/Data/LevelUpData/3066.asset
  artifactKey: Guid(61669f2d0bc26b8489e87a62164c050b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3066.asset using Guid(61669f2d0bc26b8489e87a62164c050b) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'dcbca0cc545d6dd859f9fe7d8e9cd17a') in 0.003495 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000163 seconds.
  path: Assets/Resources/Data/LevelUpData/3016.asset
  artifactKey: Guid(6172f463eb59b834ba37b77dc8e3724f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3016.asset using Guid(6172f463eb59b834ba37b77dc8e3724f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6d46610edae0a5809bb3128f417f8df0') in 0.002521 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000165 seconds.
  path: Assets/Resources/Data/SkillData/66.asset
  artifactKey: Guid(24ffa9e5fef14b24292a7486df2477fd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/66.asset using Guid(24ffa9e5fef14b24292a7486df2477fd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '7c9b9395ab0f02627b8d6a79c8f8337a') in 0.003468 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000168 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect26.png
  artifactKey: Guid(b37caf5a83877f4408584a53f720afb7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect26.png using Guid(b37caf5a83877f4408584a53f720afb7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0eccf677a4a3396fd99d58d84aa4668d') in 0.019110 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000131 seconds.
  path: Assets/Resources/Data/SkillData/46.asset
  artifactKey: Guid(0ecd0b7bfc61f3f47bc077f8566507b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/46.asset using Guid(0ecd0b7bfc61f3f47bc077f8566507b6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cd4ef81ff62d95e0a72f8d57b2f269f6') in 0.003462 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000218 seconds.
  path: Assets/Resources/Data/BuffData/136.asset
  artifactKey: Guid(d80bf52ab95a8cd47ab7cbbc08c42ad6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/136.asset using Guid(d80bf52ab95a8cd47ab7cbbc08c42ad6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '58a0176234e9edfae074c0f721ad848a') in 0.002503 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000157 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect06.png
  artifactKey: Guid(210bd5762242e124385357a1ae904b1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect06.png using Guid(210bd5762242e124385357a1ae904b1f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3a52689cfd3ede228d2864bc383e770d') in 0.015523 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000153 seconds.
  path: Assets/Resources/Data/LevelUpData/66.asset
  artifactKey: Guid(6c7252074337df243bab9320d252f077) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/66.asset using Guid(6c7252074337df243bab9320d252f077) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd7a5f629c21228bbdd91307a333cac86') in 0.002589 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000150 seconds.
  path: Assets/Resources/Data/LevelUpData/3076.asset
  artifactKey: Guid(ee050c8ff2860ce46991dd67efc2b809) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3076.asset using Guid(ee050c8ff2860ce46991dd67efc2b809) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a1837e2f7d14cb424df1084fd8d48446') in 0.003697 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000135 seconds.
  path: Assets/Resources/Data/BuffData/3063.asset
  artifactKey: Guid(aeb14dd27783be3419d683301cc7f7f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/3063.asset using Guid(aeb14dd27783be3419d683301cc7f7f1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6bef15722dd0411538c48fd1f5c706d7') in 0.003500 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000158 seconds.
  path: Assets/Resources/Data/LevelUpData/3046.asset
  artifactKey: Guid(9cc01eed565c6a943826a6767d75a142) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3046.asset using Guid(9cc01eed565c6a943826a6767d75a142) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4dc5b664564e2bb0405f912ec7330498') in 0.002629 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000149 seconds.
  path: Assets/Resources/Data/LevelUpData/67.asset
  artifactKey: Guid(7e09c872f3e82cf43a84f9729e8e1d8a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/67.asset using Guid(7e09c872f3e82cf43a84f9729e8e1d8a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f5ff57c56bc84bb7d4d851fa81655fbe') in 0.002407 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000146 seconds.
  path: Assets/Resources/Data/BuffData/146.asset
  artifactKey: Guid(787ef6831b395de45aef9e4a9db8683d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/146.asset using Guid(787ef6831b395de45aef9e4a9db8683d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '36a25654fed1f2baa3b9a86ac1cf2053') in 0.003289 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000203 seconds.
  path: Assets/Resources/Data/BuffData/3076.asset
  artifactKey: Guid(939362a95a3734a468708c7cbc048f2c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/3076.asset using Guid(939362a95a3734a468708c7cbc048f2c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8a2c327aa70ca7e0aa0bab973989ad6c') in 0.002587 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000151 seconds.
  path: Assets/Resources/Data/SkillData/76.asset
  artifactKey: Guid(6302cf8781c7a604892dec835ce76f43) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/76.asset using Guid(6302cf8781c7a604892dec835ce76f43) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ceccaefbd0b84872a3e7bcfe28ea6da3') in 0.003096 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000137 seconds.
  path: Assets/Resources/Data/SkillData/67.asset
  artifactKey: Guid(e307934485f4c664abcfe64eab299335) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/67.asset using Guid(e307934485f4c664abcfe64eab299335) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6940b482624bb7ff8f7d357e41714c7e') in 0.004062 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000181 seconds.
  path: Assets/Resources/Data/BuffData/69.asset
  artifactKey: Guid(29cc8513ff56ec640967b0143f695855) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/69.asset using Guid(29cc8513ff56ec640967b0143f695855) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd0970100ce32bb2e5dc6d7cd0931ad3a') in 0.002598 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000186 seconds.
  path: Assets/Resources/Data/LevelUpData/3086.asset
  artifactKey: Guid(6d2ce5402a0fe0d4f80b1ab983755f2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3086.asset using Guid(6d2ce5402a0fe0d4f80b1ab983755f2a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1be839bf2232bc80ea8b5f4c258b8fcf') in 0.003094 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000155 seconds.
  path: Assets/Resources/Data/SkillData/86.asset
  artifactKey: Guid(a42fd192261adb046a6d2928062aaabc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/86.asset using Guid(a42fd192261adb046a6d2928062aaabc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'bc4a235279c924d7620f90a19265a31b') in 0.003064 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000205 seconds.
  path: Assets/Resources/Data/BuffData/36.asset
  artifactKey: Guid(332ffb0b249db2a4eb9fcec8dc86d0b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/36.asset using Guid(332ffb0b249db2a4eb9fcec8dc86d0b7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1378e1fc7fa52280f8cd1e8b1e18ccab') in 0.002708 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000206 seconds.
  path: Assets/Resources/Data/BuffData/64.asset
  artifactKey: Guid(f63cfec78bd0883428b6a94d18d6de33) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/64.asset using Guid(f63cfec78bd0883428b6a94d18d6de33) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '994f486d63b43e83ca048f713586b22f') in 0.002537 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000185 seconds.
  path: Assets/Resources/Data/LevelUpData/69.asset
  artifactKey: Guid(072ffdee11cc6e748b8abb579c19de5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/69.asset using Guid(072ffdee11cc6e748b8abb579c19de5e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '54f1a70590a71f05ee8212aba29ab4c4') in 0.002697 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000148 seconds.
  path: Assets/Resources/Data/LevelUpData/96.asset
  artifactKey: Guid(5712978b0272a9a4a98097eeb01d54c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/96.asset using Guid(5712978b0272a9a4a98097eeb01d54c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '41962ac55c4480dcd92c0dcec373bd91') in 0.003442 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000189 seconds.
  path: Assets/Resources/Data/LevelUpData/3062.asset
  artifactKey: Guid(af82aa610c3666e47a9f1c06d4094c6a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3062.asset using Guid(af82aa610c3666e47a9f1c06d4094c6a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '785c125516e43d7246eb84dfd739ec0e') in 0.003356 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000192 seconds.
  path: Assets/Resources/Data/LevelUpData/3064.asset
  artifactKey: Guid(fcc60da18c93d8a458a2ccbaa4c8742e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3064.asset using Guid(fcc60da18c93d8a458a2ccbaa4c8742e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '22b02a1c18e9ff5defa2875d290db2a6') in 0.002768 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000194 seconds.
  path: Assets/Resources/Data/LevelUpData/3067.asset
  artifactKey: Guid(cffe11127fb16c5419d0bdcb806e1fe0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3067.asset using Guid(cffe11127fb16c5419d0bdcb806e1fe0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '540ba64f18e1aa53895c069081396178') in 0.003616 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000200 seconds.
  path: Assets/Resources/Data/SkillData/96.asset
  artifactKey: Guid(244894138b2de6746b52edc064e15043) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/96.asset using Guid(244894138b2de6746b52edc064e15043) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '1f3ef613f5dcd3698d17bbcee13f511b') in 0.003095 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000176 seconds.
  path: Assets/Art/Sprites/Tower/windtower_attack/windtower_gif06.png
  artifactKey: Guid(1ccc26cc2b30eb74db54dea78ef1c23a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/windtower_attack/windtower_gif06.png using Guid(1ccc26cc2b30eb74db54dea78ef1c23a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '0327d3d0673d2a9a2fa23f983a896854') in 0.016979 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000158 seconds.
  path: Assets/Resources/Data/LevelUpData/76.asset
  artifactKey: Guid(d51e597f5acb9254eb9be229d9caa85c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/76.asset using Guid(d51e597f5acb9254eb9be229d9caa85c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'fa9e9dc22e3d56361a92c439b0dbfdb6') in 0.002887 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000163 seconds.
  path: Assets/Resources/Data/LevelUpData/3068.asset
  artifactKey: Guid(16887b8685e99614db47f36559fffb33) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3068.asset using Guid(16887b8685e99614db47f36559fffb33) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'caff69c3babcdd3b1795ace8a8681555') in 0.003236 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000163 seconds.
  path: Assets/Resources/Data/BuffData/63.asset
  artifactKey: Guid(8e542a3df937c4e49b2be9712693de77) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/63.asset using Guid(8e542a3df937c4e49b2be9712693de77) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'ad60fd7447ecec476f5441e0e6785827') in 0.002615 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000162 seconds.
  path: Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect46.png
  artifactKey: Guid(94f12922cbc6aa746ba3562deed090fd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Art/Sprites/Tower/lighttower_effect/lighttower_effect46.png using Guid(94f12922cbc6aa746ba3562deed090fd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c955e500352fb69d22abe7b4bca97a6a') in 0.009782 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000159 seconds.
  path: Assets/Resources/Data/SkillData/64.asset
  artifactKey: Guid(46bdd95b9bd9b0042aee87999b1b7dd5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/64.asset using Guid(46bdd95b9bd9b0042aee87999b1b7dd5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b60bb85834837283e7e2b488b871777d') in 0.003452 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000205 seconds.
  path: Assets/Resources/Data/LevelUpData/68.asset
  artifactKey: Guid(ee0ae14a7aa2ae247a18bf7ae5023ff8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/68.asset using Guid(ee0ae14a7aa2ae247a18bf7ae5023ff8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6e9bc7b194c350229756a00a55825e71') in 0.002634 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000175 seconds.
  path: Assets/Resources/Data/SkillData/3006.asset
  artifactKey: Guid(972f17c9822ad0c488ddc76cfd9fcd46) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/SkillData/3006.asset using Guid(972f17c9822ad0c488ddc76cfd9fcd46) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '6bf79146719216a84cf486e639381f99') in 0.002849 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000202 seconds.
  path: Assets/Resources/Data/LevelUpData/3063.asset
  artifactKey: Guid(61e01cd2e4372104c94d267c5f60b016) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3063.asset using Guid(61e01cd2e4372104c94d267c5f60b016) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'e44bea0862027bc51588328236118878') in 0.002868 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000169 seconds.
  path: Assets/Resources/Data/BuffData/3066.asset
  artifactKey: Guid(46dfc64b890aff446ac4585d521f15e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/3066.asset using Guid(46dfc64b890aff446ac4585d521f15e6) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '795d59cd7a3108a4e42e7caf24e9f30f') in 0.002783 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000182 seconds.
  path: Assets/Resources/Data/LevelUpData/3036.asset
  artifactKey: Guid(f5fca6a29bb04df45866aaf5e55e9711) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3036.asset using Guid(f5fca6a29bb04df45866aaf5e55e9711) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c506487b2c518510455d44b853e52ba3') in 0.002634 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000157 seconds.
  path: Assets/Resources/Data/BuffData/66.asset
  artifactKey: Guid(343036fa41c97de4bb7e99ec478dfb1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/66.asset using Guid(343036fa41c97de4bb7e99ec478dfb1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '29673f964ad39dbafea4a9044edf5093') in 0.003299 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000253 seconds.
  path: Assets/Resources/Data/BuffData/3069.asset
  artifactKey: Guid(e2446a1f2c41ae4469804f052f205bb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/3069.asset using Guid(e2446a1f2c41ae4469804f052f205bb5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f1fefbfefecaa0d96bf7d1f8338090f9') in 0.002543 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000149 seconds.
  path: Assets/Resources/Data/LevelUpData/3006.asset
  artifactKey: Guid(524f078245b85e6488f12bbb7439f2dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/3006.asset using Guid(524f078245b85e6488f12bbb7439f2dd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3db173e5980c7507b1ae2007ea903c4a') in 0.002682 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000162 seconds.
  path: Assets/Resources/Data/LevelUpData/36.asset
  artifactKey: Guid(4e30fccffce101c4b92a56f9449dc1bd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/LevelUpData/36.asset using Guid(4e30fccffce101c4b92a56f9449dc1bd) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4b05582fb7240779a839c9ebe4a8a5a6') in 0.002747 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000205 seconds.
  path: Assets/Resources/Data/BuffData/60.asset
  artifactKey: Guid(3e88f38580332ca498e6a8e429efe2c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Data/BuffData/60.asset using Guid(3e88f38580332ca498e6a8e429efe2c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'f566c55a792738386aada01fdb904c67') in 0.003050 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.012173 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.87 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.118 seconds
Domain Reload Profiling:
	ReloadAssembly (1118ms)
		BeginReloadAssembly (105ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (38ms)
		EndReloadAssembly (980ms)
			LoadAssemblies (103ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (269ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (47ms)
			SetupLoadedEditorAssemblies (477ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (81ms)
				ProcessInitializeOnLoadAttributes (381ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.1 MB.
System memory in use after: 274.5 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6979.
Total: 3.619000 ms (FindLiveObjects: 0.320400 ms CreateObjectMapping: 0.167900 ms MarkObjects: 3.101400 ms  DeleteObjects: 0.028600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 127.882395 seconds.
  path: Assets/Scripts/SkillSystem/SkillReleaseChecker/CheckBuffStackLayerSkillReleaseChecker.cs
  artifactKey: Guid(2fa2207b9e11a5c418b25e1c7f437169) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/SkillSystem/SkillReleaseChecker/CheckBuffStackLayerSkillReleaseChecker.cs using Guid(2fa2207b9e11a5c418b25e1c7f437169) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '814390149411b735f1897f50b4d55124') in 0.006079 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.011817 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.93 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.096 seconds
Domain Reload Profiling:
	ReloadAssembly (1096ms)
		BeginReloadAssembly (99ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (35ms)
		EndReloadAssembly (962ms)
			LoadAssemblies (97ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (275ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (49ms)
			SetupLoadedEditorAssemblies (469ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (81ms)
				ProcessInitializeOnLoadAttributes (374ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.01 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.1 MB.
System memory in use after: 274.6 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6982.
Total: 3.631300 ms (FindLiveObjects: 0.336500 ms CreateObjectMapping: 0.172100 ms MarkObjects: 3.092800 ms  DeleteObjects: 0.028900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 42.948164 seconds.
  path: Assets/Scripts/SkillSystem/SkillReleaseChecker/CheckBuffStackLayerSkillReleaseChecker.cs
  artifactKey: Guid(2fa2207b9e11a5c418b25e1c7f437169) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/SkillSystem/SkillReleaseChecker/CheckBuffStackLayerSkillReleaseChecker.cs using Guid(2fa2207b9e11a5c418b25e1c7f437169) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '05cb99156144f3334499153abbe6ac42') in 0.006315 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013606 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.099 seconds
Domain Reload Profiling:
	ReloadAssembly (1100ms)
		BeginReloadAssembly (139ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (56ms)
		EndReloadAssembly (928ms)
			LoadAssemblies (94ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (261ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (45ms)
			SetupLoadedEditorAssemblies (457ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (79ms)
				ProcessInitializeOnLoadAttributes (363ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.1 MB.
System memory in use after: 274.6 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6985.
Total: 3.634000 ms (FindLiveObjects: 0.346100 ms CreateObjectMapping: 0.175100 ms MarkObjects: 3.086400 ms  DeleteObjects: 0.025600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.017103 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.01 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.098 seconds
Domain Reload Profiling:
	ReloadAssembly (1098ms)
		BeginReloadAssembly (106ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (39ms)
		EndReloadAssembly (959ms)
			LoadAssemblies (95ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (272ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (52ms)
			SetupLoadedEditorAssemblies (459ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (82ms)
				ProcessInitializeOnLoadAttributes (362ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.48 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.1 MB.
System memory in use after: 274.6 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6988.
Total: 3.599000 ms (FindLiveObjects: 0.316900 ms CreateObjectMapping: 0.160000 ms MarkObjects: 3.094500 ms  DeleteObjects: 0.026700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.012644 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.95 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.102 seconds
Domain Reload Profiling:
	ReloadAssembly (1102ms)
		BeginReloadAssembly (99ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (34ms)
		EndReloadAssembly (967ms)
			LoadAssemblies (99ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (285ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (48ms)
			SetupLoadedEditorAssemblies (463ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (78ms)
				ProcessInitializeOnLoadAttributes (370ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.1 MB.
System memory in use after: 274.6 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6991.
Total: 3.613700 ms (FindLiveObjects: 0.337200 ms CreateObjectMapping: 0.179700 ms MarkObjects: 3.071100 ms  DeleteObjects: 0.024900 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.006359 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.17 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.206 seconds
Domain Reload Profiling:
	ReloadAssembly (1206ms)
		BeginReloadAssembly (109ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (35ms)
		EndReloadAssembly (1061ms)
			LoadAssemblies (109ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (296ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (54ms)
			SetupLoadedEditorAssemblies (501ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (84ms)
				ProcessInitializeOnLoadAttributes (400ms)
				ProcessInitializeOnLoadMethodAttributes (12ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (10ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.1 MB.
System memory in use after: 274.6 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 6994.
Total: 3.717300 ms (FindLiveObjects: 0.315600 ms CreateObjectMapping: 0.164700 ms MarkObjects: 3.207700 ms  DeleteObjects: 0.028500 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 202.495531 seconds.
  path: Assets/Scripts/SkillSystem/SkillReleaseChecker/CheckBuffStackLayerSkillReleaseChecker.cs
  artifactKey: Guid(2fa2207b9e11a5c418b25e1c7f437169) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/SkillSystem/SkillReleaseChecker/CheckBuffStackLayerSkillReleaseChecker.cs using Guid(2fa2207b9e11a5c418b25e1c7f437169) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '40187c0200d65a05b9366689ca5608b9') in 0.007596 seconds 
========================================================================
Received Import Request.
  Time since last request: 2.435086 seconds.
  path: Assets/Resources/Prefabs/Projectile/Bullet.prefab
  artifactKey: Guid(645ca3610834dc04989aef51aad84502) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/Bullet.prefab using Guid(645ca3610834dc04989aef51aad84502) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'a1b540181c68a93cfbeb5964a894497c') in 0.038615 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000223 seconds.
  path: Assets/Resources/Prefabs/Projectile/IceBullet.prefab
  artifactKey: Guid(550e0a325d8f09f41992186f3a662d61) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/IceBullet.prefab using Guid(550e0a325d8f09f41992186f3a662d61) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c62dd1e0d7faf93f467a4a65ffa128c9') in 0.008574 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000323 seconds.
  path: Assets/Resources/Prefabs/Projectile/TestBullet.prefab
  artifactKey: Guid(18e62628049aacf45a6d86485301b4a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/TestBullet.prefab using Guid(18e62628049aacf45a6d86485301b4a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'cc55363e97fbfee9716c6656ccdd5cd9') in 0.007435 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000174 seconds.
  path: Assets/Resources/Prefabs/Projectile/StandardBullet.prefab
  artifactKey: Guid(30c14623fe03ccd4794e618295d64aba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/StandardBullet.prefab using Guid(30c14623fe03ccd4794e618295d64aba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '4e38dd87bc287aeb92f99e7084d89393') in 0.011510 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000190 seconds.
  path: Assets/Resources/Prefabs/Projectile/ReinforceWindBullet.prefab
  artifactKey: Guid(2268ec1c060c2134ebb4f9c3648ca5cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/ReinforceWindBullet.prefab using Guid(2268ec1c060c2134ebb4f9c3648ca5cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b2c4399606060655282fc3bfecc4e92b') in 0.007204 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000175 seconds.
  path: Assets/Resources/Prefabs/Projectile/WoodBullet.prefab
  artifactKey: Guid(ca4e287894e0b7a4691bad26ebc8376c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/WoodBullet.prefab using Guid(ca4e287894e0b7a4691bad26ebc8376c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'd6feb33cfee99fd940573df4ca2a3f36') in 0.007273 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000209 seconds.
  path: Assets/Resources/Prefabs/Projectile/FireBullet.prefab
  artifactKey: Guid(a2b8b643f54bd7042bbffa2aa6854841) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/FireBullet.prefab using Guid(a2b8b643f54bd7042bbffa2aa6854841) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3ed27c0d9252c4c2e453e7d83582093c') in 0.008676 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000169 seconds.
  path: Assets/Resources/Prefabs/Projectile/WindBullet.prefab
  artifactKey: Guid(630f50a79761442469a08926956f0936) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/WindBullet.prefab using Guid(630f50a79761442469a08926956f0936) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '61e8363f143319edf5b4f309dc4db1c5') in 0.014156 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000264 seconds.
  path: Assets/Resources/Prefabs/Projectile/DarkBullet.prefab
  artifactKey: Guid(8a479a4ab39d5ce4da076903888ceca8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/DarkBullet.prefab using Guid(8a479a4ab39d5ce4da076903888ceca8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '5aa67965d46bd1017a48c0e4d9b03e4a') in 0.009632 seconds 
========================================================================
Received Import Request.
  Time since last request: 1.105413 seconds.
  path: Assets/Scripts/attributeProperty/Projectile/Bullet.cs
  artifactKey: Guid(25c1410f5d73365438a78d58916dda22) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/attributeProperty/Projectile/Bullet.cs using Guid(25c1410f5d73365438a78d58916dda22) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'c57e2d6583098d8d54060d63a15d595f') in 0.002173 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.012582 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.95 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.080 seconds
Domain Reload Profiling:
	ReloadAssembly (1080ms)
		BeginReloadAssembly (100ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (5ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (34ms)
		EndReloadAssembly (946ms)
			LoadAssemblies (99ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (263ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (47ms)
			SetupLoadedEditorAssemblies (466ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (81ms)
				ProcessInitializeOnLoadAttributes (371ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.2 MB.
System memory in use after: 274.6 MB.

Unloading 35 unused Assets to reduce memory usage. Loaded Objects now: 6998.
Total: 3.703100 ms (FindLiveObjects: 0.315600 ms CreateObjectMapping: 0.177100 ms MarkObjects: 3.141300 ms  DeleteObjects: 0.068300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013624 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.96 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.096 seconds
Domain Reload Profiling:
	ReloadAssembly (1097ms)
		BeginReloadAssembly (96ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (32ms)
		EndReloadAssembly (968ms)
			LoadAssemblies (102ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (271ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (47ms)
			SetupLoadedEditorAssemblies (475ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (80ms)
				ProcessInitializeOnLoadAttributes (379ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.2 MB.
System memory in use after: 274.7 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 7001.
Total: 3.527500 ms (FindLiveObjects: 0.349700 ms CreateObjectMapping: 0.153100 ms MarkObjects: 3.000000 ms  DeleteObjects: 0.024000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.011765 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.90 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.113 seconds
Domain Reload Profiling:
	ReloadAssembly (1113ms)
		BeginReloadAssembly (122ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (41ms)
		EndReloadAssembly (958ms)
			LoadAssemblies (96ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (277ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (49ms)
			SetupLoadedEditorAssemblies (463ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (78ms)
				ProcessInitializeOnLoadAttributes (369ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.2 MB.
System memory in use after: 274.7 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 7004.
Total: 3.648200 ms (FindLiveObjects: 0.325100 ms CreateObjectMapping: 0.169400 ms MarkObjects: 3.126200 ms  DeleteObjects: 0.026600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018143 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.84 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.087 seconds
Domain Reload Profiling:
	ReloadAssembly (1087ms)
		BeginReloadAssembly (109ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (32ms)
		EndReloadAssembly (944ms)
			LoadAssemblies (94ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (269ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (46ms)
			SetupLoadedEditorAssemblies (466ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (78ms)
				ProcessInitializeOnLoadAttributes (374ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.60 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.2 MB.
System memory in use after: 274.7 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 7007.
Total: 4.008100 ms (FindLiveObjects: 0.353100 ms CreateObjectMapping: 0.234700 ms MarkObjects: 3.374500 ms  DeleteObjects: 0.044300 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.007629 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.04 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.099 seconds
Domain Reload Profiling:
	ReloadAssembly (1099ms)
		BeginReloadAssembly (98ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (33ms)
		EndReloadAssembly (968ms)
			LoadAssemblies (96ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (277ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (46ms)
			SetupLoadedEditorAssemblies (478ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (83ms)
				ProcessInitializeOnLoadAttributes (376ms)
				ProcessInitializeOnLoadMethodAttributes (13ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.07 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.2 MB.
System memory in use after: 274.7 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 7010.
Total: 3.837800 ms (FindLiveObjects: 0.392000 ms CreateObjectMapping: 0.172500 ms MarkObjects: 3.246700 ms  DeleteObjects: 0.025400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Import Request.
  Time since last request: 354.481513 seconds.
  path: Assets/Resources/Prefabs/Projectile/IceBullet.prefab
  artifactKey: Guid(550e0a325d8f09f41992186f3a662d61) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/IceBullet.prefab using Guid(550e0a325d8f09f41992186f3a662d61) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '3688a42991bea950d0cbed9038f5a2d4') in 0.040016 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000327 seconds.
  path: Assets/Resources/Prefabs/Projectile/TestBullet.prefab
  artifactKey: Guid(18e62628049aacf45a6d86485301b4a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/TestBullet.prefab using Guid(18e62628049aacf45a6d86485301b4a7) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '364aa009ec7d472e1655792829db6551') in 0.006688 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000142 seconds.
  path: Assets/Resources/Prefabs/Projectile/StandardBullet.prefab
  artifactKey: Guid(30c14623fe03ccd4794e618295d64aba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/StandardBullet.prefab using Guid(30c14623fe03ccd4794e618295d64aba) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '755958aa23b7a3599a66dd791d6a01f9') in 0.009244 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000264 seconds.
  path: Assets/Resources/Prefabs/Projectile/ReinforceWindBullet.prefab
  artifactKey: Guid(2268ec1c060c2134ebb4f9c3648ca5cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/ReinforceWindBullet.prefab using Guid(2268ec1c060c2134ebb4f9c3648ca5cb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '8336e94e553e270280e837224002cb55') in 0.008182 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000147 seconds.
  path: Assets/Resources/Prefabs/Projectile/WoodBullet.prefab
  artifactKey: Guid(ca4e287894e0b7a4691bad26ebc8376c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/WoodBullet.prefab using Guid(ca4e287894e0b7a4691bad26ebc8376c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: 'b3e8a53c3d15f1bd9dcfe25f6e05ec1b') in 0.006581 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000147 seconds.
  path: Assets/Resources/Prefabs/Projectile/FireBullet.prefab
  artifactKey: Guid(a2b8b643f54bd7042bbffa2aa6854841) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/FireBullet.prefab using Guid(a2b8b643f54bd7042bbffa2aa6854841) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '747122e70beb9449c0ff30da4c6006d0') in 0.008219 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000196 seconds.
  path: Assets/Resources/Prefabs/Projectile/WindBullet.prefab
  artifactKey: Guid(630f50a79761442469a08926956f0936) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/WindBullet.prefab using Guid(630f50a79761442469a08926956f0936) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '298a5cdc86be6869063fc624d3957156') in 0.012130 seconds 
========================================================================
Received Import Request.
  Time since last request: 0.000140 seconds.
  path: Assets/Resources/Prefabs/Projectile/DarkBullet.prefab
  artifactKey: Guid(8a479a4ab39d5ce4da076903888ceca8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Resources/Prefabs/Projectile/DarkBullet.prefab using Guid(8a479a4ab39d5ce4da076903888ceca8) Importer(815301076,1909f56bfc062723c751e8b465ee728b)  -> (artifact id: '53ac85d3ce28a98b4670237293bc64a6') in 0.009517 seconds 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.012566 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.115 seconds
Domain Reload Profiling:
	ReloadAssembly (1116ms)
		BeginReloadAssembly (121ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (6ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (47ms)
		EndReloadAssembly (960ms)
			LoadAssemblies (106ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (280ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (50ms)
			SetupLoadedEditorAssemblies (466ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (77ms)
				ProcessInitializeOnLoadAttributes (374ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.2 MB.
System memory in use after: 274.7 MB.

Unloading 35 unused Assets to reduce memory usage. Loaded Objects now: 7014.
Total: 6.204700 ms (FindLiveObjects: 0.642300 ms CreateObjectMapping: 0.317300 ms MarkObjects: 5.138100 ms  DeleteObjects: 0.105200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.018583 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.091 seconds
Domain Reload Profiling:
	ReloadAssembly (1092ms)
		BeginReloadAssembly (105ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (38ms)
		EndReloadAssembly (953ms)
			LoadAssemblies (98ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (273ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (46ms)
			SetupLoadedEditorAssemblies (463ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (80ms)
				ProcessInitializeOnLoadAttributes (368ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.43 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.2 MB.
System memory in use after: 274.7 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 7017.
Total: 3.640300 ms (FindLiveObjects: 0.326300 ms CreateObjectMapping: 0.162500 ms MarkObjects: 3.124900 ms  DeleteObjects: 0.025700 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.012735 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.89 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.132 seconds
Domain Reload Profiling:
	ReloadAssembly (1132ms)
		BeginReloadAssembly (128ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (34ms)
		EndReloadAssembly (971ms)
			LoadAssemblies (93ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (281ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (46ms)
			SetupLoadedEditorAssemblies (476ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (86ms)
				ProcessInitializeOnLoadAttributes (375ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.46 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.2 MB.
System memory in use after: 274.7 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 7020.
Total: 3.597600 ms (FindLiveObjects: 0.320600 ms CreateObjectMapping: 0.155900 ms MarkObjects: 3.094100 ms  DeleteObjects: 0.026100 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.012283 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.21 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.134 seconds
Domain Reload Profiling:
	ReloadAssembly (1134ms)
		BeginReloadAssembly (127ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (48ms)
		EndReloadAssembly (973ms)
			LoadAssemblies (111ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (285ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (46ms)
			SetupLoadedEditorAssemblies (468ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (3ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (81ms)
				ProcessInitializeOnLoadAttributes (370ms)
				ProcessInitializeOnLoadMethodAttributes (11ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (9ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.3 MB.
System memory in use after: 274.7 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 7023.
Total: 3.600500 ms (FindLiveObjects: 0.340900 ms CreateObjectMapping: 0.174000 ms MarkObjects: 3.055700 ms  DeleteObjects: 0.029200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.014169 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.35 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.100 seconds
Domain Reload Profiling:
	ReloadAssembly (2100ms)
		BeginReloadAssembly (185ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (8ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (59ms)
		EndReloadAssembly (1859ms)
			LoadAssemblies (214ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (473ms)
			ReleaseScriptCaches (3ms)
			RebuildScriptCaches (89ms)
			SetupLoadedEditorAssemblies (970ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (4ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (179ms)
				ProcessInitializeOnLoadAttributes (774ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (23ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.3 MB.
System memory in use after: 274.8 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 7026.
Total: 3.657600 ms (FindLiveObjects: 0.322200 ms CreateObjectMapping: 0.161800 ms MarkObjects: 3.147700 ms  DeleteObjects: 0.025200 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.006924 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.91 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.130 seconds
Domain Reload Profiling:
	ReloadAssembly (1130ms)
		BeginReloadAssembly (102ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (36ms)
		EndReloadAssembly (995ms)
			LoadAssemblies (101ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (280ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (68ms)
			SetupLoadedEditorAssemblies (470ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (79ms)
				ProcessInitializeOnLoadAttributes (376ms)
				ProcessInitializeOnLoadMethodAttributes (10ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (8ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.45 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.3 MB.
System memory in use after: 274.8 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 7029.
Total: 3.711000 ms (FindLiveObjects: 0.310400 ms CreateObjectMapping: 0.161400 ms MarkObjects: 3.208800 ms  DeleteObjects: 0.029000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.013288 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 0.94 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.119 seconds
Domain Reload Profiling:
	ReloadAssembly (1119ms)
		BeginReloadAssembly (103ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (4ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (33ms)
		EndReloadAssembly (982ms)
			LoadAssemblies (103ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (283ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (48ms)
			SetupLoadedEditorAssemblies (466ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (2ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (79ms)
				ProcessInitializeOnLoadAttributes (368ms)
				ProcessInitializeOnLoadMethodAttributes (14ms)
				AfterProcessingInitializeOnLoad (1ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (14ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 0.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.3 MB.
System memory in use after: 274.8 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 7032.
Total: 3.796900 ms (FindLiveObjects: 0.322200 ms CreateObjectMapping: 0.178200 ms MarkObjects: 3.267300 ms  DeleteObjects: 0.028600 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.016129 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.28 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  1.944 seconds
Domain Reload Profiling:
	ReloadAssembly (1944ms)
		BeginReloadAssembly (156ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (7ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (45ms)
		EndReloadAssembly (1728ms)
			LoadAssemblies (202ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (467ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (99ms)
			SetupLoadedEditorAssemblies (848ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (4ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (143ms)
				ProcessInitializeOnLoadAttributes (670ms)
				ProcessInitializeOnLoadMethodAttributes (26ms)
				AfterProcessingInitializeOnLoad (2ms)
				EditorAssembliesLoaded (0ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (14ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.04 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.3 MB.
System memory in use after: 274.8 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 7035.
Total: 3.764700 ms (FindLiveObjects: 0.346400 ms CreateObjectMapping: 0.175000 ms MarkObjects: 3.215100 ms  DeleteObjects: 0.027400 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
========================================================================
Received Prepare
Registering precompiled user dll's ...
Registered in 0.015086 seconds.
Begin MonoManager ReloadAssembly
Native extension for WindowsStandalone target not found
Refreshing native plugins compatible for Editor in 1.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Mono: successfully reloaded assembly
- Completed reload, in  2.047 seconds
Domain Reload Profiling:
	ReloadAssembly (2048ms)
		BeginReloadAssembly (186ms)
			ExecutionOrderSort (0ms)
			DisableScriptedObjects (9ms)
			BackupInstance (0ms)
			ReleaseScriptingObjects (0ms)
			CreateAndSetChildDomain (48ms)
		EndReloadAssembly (1798ms)
			LoadAssemblies (206ms)
			RebuildTransferFunctionScriptingTraits (0ms)
			SetupTypeCache (488ms)
			ReleaseScriptCaches (2ms)
			RebuildScriptCaches (93ms)
			SetupLoadedEditorAssemblies (901ms)
				LogAssemblyErrors (0ms)
				InitializePlatformSupportModulesInManaged (4ms)
				SetLoadedEditorAssemblies (0ms)
				RefreshPlugins (1ms)
				BeforeProcessingInitializeOnLoad (152ms)
				ProcessInitializeOnLoadAttributes (693ms)
				ProcessInitializeOnLoadMethodAttributes (45ms)
				AfterProcessingInitializeOnLoad (5ms)
				EditorAssembliesLoaded (1ms)
			ExecutionOrderSort2 (0ms)
			AwakeInstancesAfterBackupRestoration (18ms)
Platform modules already initialized, skipping
Refreshing native plugins compatible for Editor in 1.47 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 6257 Unused Serialized files (Serialized files now loaded: 0)
System memory in use before: 274.3 MB.
System memory in use after: 274.8 MB.

Unloading 33 unused Assets to reduce memory usage. Loaded Objects now: 7038.
Total: 6.466800 ms (FindLiveObjects: 0.850100 ms CreateObjectMapping: 0.283700 ms MarkObjects: 5.291000 ms  DeleteObjects: 0.040000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
AssetImportWorkerClient::OnTransportError - code=2 error=End of file
