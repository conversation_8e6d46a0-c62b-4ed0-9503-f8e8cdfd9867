using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using System.Reflection;
using System;
public class BaseTower : AttributeProperty
{
    //======== DATA ========
    private CharacterStats _characterStats;
    //HEALTH
    private float _healthChangeTime;
    public TMP_Text debugInfo;
    //Attack 
    protected float _attackGap;
    //Position
    protected Rigidbody2D rigidbody2d;
    //Animation
    protected Animator animator;
    protected Vector2 lookDirection = new Vector2(1, 0);

    protected virtual void Awake()
    {
        // ======= INIT ===========
        _characterStats = GetComponent<CharacterStats>();
        InitTowerStats();
    }

    protected override void Start()
    {
        base.Start();
        // =========== MOVEMENT ==============
        rigidbody2d = GetComponent<Rigidbody2D>();
        // ======== SKILL ==========
        _skillSystem = GetComponent<CharacterSkillSystem>();

        // ======== HEALTH ==========
        invincibleTimer = -1.0f;
        _health = maxHealth;
        _attackGap = 0;


        // ==== ANIMATION =====
        //animator = GetComponent<Animator>();

        // ==== AUDIO =====
        //audioSource = GetComponent<AudioSource>();

    }
    protected override void Update()
    {
        base.Update();
        // ================= HEALTH ====================
        if (isInvincible)
        {
            invincibleTimer -= Time.deltaTime;
            if (invincibleTimer < 0)
                isInvincible = false;
        }

        UpdateTowerDebugInfo(this);
        UpdateHealth();
        SustainableAuraTrigger();
        // ============== ATTACK =======================
        _attackGap = Mathf.Clamp(_attackGap, 0, 1 / _validAttackSpeed); //修正攻击间隔，这里是为了防止有的buff造成攻速过慢，buff消失后长时间无法攻击
        Collider2D[] enemys = TargetInRange();
        Attack(enemys);

        // ============== ANIMATION =======================
        // ============== PROJECTILE ======================
    }


    public void UpdateTowerDebugInfo(BaseTower tower)
    {
        FieldInfo[] fields = tower.GetType().GetFields(BindingFlags.Public | BindingFlags.Instance);

        string debug_str = "";
        // 遍历所有公共字段并打印
        foreach (FieldInfo field in fields)
        {
            debug_str += $"{field.Name} = {field.GetValue(tower)}\n";
        }

        //PropertyInfo[] properties = tower.GetType().GetProperties();
        //foreach (PropertyInfo prorerty in properties)
        //{
        //    if (prorerty.PropertyType.IsPrimitive || prorerty.PropertyType == typeof(string))
        //        debug_str += $"{prorerty.Name} = {prorerty.GetValue(tower)}\n";
        //}
        debugInfo.text = _health.ToString();
    }

    // ============== ATTACK =======================
    protected virtual Collider2D[] TargetInRange()
    {
        //检测敌人图层圆形范围内最近的敌对目标作为攻击方向
        Collider2D[]  colliders = Physics2D.OverlapCircleAll(rigidbody2d.position, attackRange, LayerMask.GetMask("Enemy"));
        return colliders;
    }

    protected virtual void Attack(Collider2D[] enemys) 
    {
    }

    // ===================== HEALTH ==================

    public override void ChangeHealth(int amount)
    {
        if (amount <= 0)
        {
            if (isInvincible)
                return;
            int _validDamage = Mathf.FloorToInt(amount * takeDamagePct);
            _health = Mathf.Clamp(_health + _validDamage, 0, _validMaxHealth);
            isInvincible = true;
            invincibleTimer = timeInvincible;
            HurtTrigger();
        }
        else
        {

            _overHeal = _health + amount - _validMaxHealth;
            _health = Mathf.Clamp(_health + amount, 0, _validMaxHealth);
            if (_overHeal > 0)
            {
                OverHealTrigger();
            }
        }

        if (_health <= 0)
        {
            Destroy(gameObject);
        }
        //UIHealthBar.Instance.SetValue(currentHealth / (float)maxHealth);
    }
    void OnDrawGizmos()
    {
        // 中心点/检测范围
        Gizmos.color = Color.black;
        Gizmos.DrawWireSphere(GetComponent<Transform>().position, attackRange);
    }

    protected virtual void InitTowerStats()
    {
        // 一级属性
        characterType = _characterStats.characterType;
        // ======== HEALTH ==========
        invincibleTimer = -1.0f;
        maxHealth = _characterStats.maxHealth;
        _health = _characterStats.maxHealth;
        timeInvincible = _characterStats.timeInvincible;
        //========= ATTACK =================
        attackSpeed = _characterStats.attackSpeed;
        attackDamage = _characterStats.attackDamage;
        attackRange = _characterStats.attackRange;
        _attackGap = 0.0f;
        bulletSpeed = _characterStats.bulletSpeed;

        //二级属性
        // ======== HEALTH ==========
        attackSpeedPct = _characterStats.attackSpeedPct; //百分比攻速加成
        attackDamagePct = _characterStats.attackDamagePct; //百分比攻击加成
        healthChangePerSecond = _characterStats.healthChangePerSecond; //每秒生命变化
        healthChangePerSecondPct = 0; //每秒生命变化
        takeDamagePct = _characterStats.takeDamagePct; //百分比增伤
    }

    public void UpdateHealth()
    {
        if ((_healthChangeTime <= 0.0f) & (healthChangePerSecond != 0))
        {
            ChangeHealth(healthChangePerSecond);
            _healthChangeTime = 1.0f;
        }
        else
        {
            _healthChangeTime -= Time.deltaTime;
        }
    }

    public void ResetAttackGap()
    {
        _attackGap = 1 / _validAttackSpeed;
    }

} 
